import { chatSchema } from "@/schema/chat";
import { z } from "zod";

export type CreatedMessage = {
  senderId: string;
  roomId?: string; // NOTE: not sure if this value is coming in the response
  groupId?: string; // NOTE: not sure if this value is coming for every chat interfaces
  // name: string;
  profileUrl?: string;
  text?: string;
  imageUrl?: string;
  timestamp: string;
  readBy: string;
  sender: {
    email?: string;
    name: string;
  };
  stageName?: string;
  stageGroupId?: string;
};

export type Message = z.infer<typeof chatSchema>;

export type ServerToClientEvents = {
  // eslint-disable-next-line no-unused-vars
  newMessage: (message: CreatedMessage, callback: () => void) => void;
};

export type ClientToServerEvents = {
  // eslint-disable-next-line no-unused-vars
  joinGroup: (groupId: string) => void;
  // eslint-disable-next-line no-unused-vars
  createMessage: (message: Message) => void;
};
