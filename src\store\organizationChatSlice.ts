import { StateCreator } from "zustand";

import { CreatedMessage } from "@/types/Socket";

export interface OrganizationChatSlice {
  organizationChatMessages: { [id: string]: CreatedMessage[] };
  getOrganizationChatMessagesByChatId: (chatId: string) => CreatedMessage[];
  updateOrganizationChatMessages: (newMessages: {
    [id: string]: CreatedMessage[];
  }) => void;
  replaceOrganizationChatMessagesWithChatId: (
    chatId: string,
    newMessages: CreatedMessage[],
  ) => void;
}

export const organizationChatSlice: StateCreator<OrganizationChatSlice> = (
  set,
  get,
) => ({
  organizationChatMessages: {},
  getOrganizationChatMessagesByChatId: (chatId) => {
    return get().organizationChatMessages[chatId];
  },
  updateOrganizationChatMessages: (newMessages) => {
    set({ organizationChatMessages: newMessages });
  },

  replaceOrganizationChatMessagesWithChatId: (chatId, newMessages) => {
    set({
      organizationChatMessages: {
        ...get().organizationChatMessages,
        [chatId]: newMessages,
      },
    });
  },
});
