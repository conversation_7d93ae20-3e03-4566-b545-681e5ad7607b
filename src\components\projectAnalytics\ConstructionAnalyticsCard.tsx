import React, { ComponentPropsWithoutRef, useState } from "react";
import { format } from "date-fns";
import { Progress } from "@/components/ui/progress";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ProjectAnalytics, ProjectAnalyticsTotal } from "@/types/Project";
import ProductivityIcon from "../icons/ProductivityIcon";
import UsersIcon from "../icons/usersIcon";
import CalenderIcon from "../icons/calenderIcon";
import DayOfCompletionStatusBadge from "./DayOfCompletionStatusBadge";
import FlagIcon from "../icons/Flag";
import { ChevronRight, MapPin } from "lucide-react";
import ContractorRatingDialog from "../dashboard/ContractorRatingDialog";

type ProjectCardProps = ComponentPropsWithoutRef<"div"> & {
  project: ProjectAnalyticsTotal;
  data: ProjectAnalytics;
  projectScope: any;
  projectDetail: any;
};

const ConstructionDetailsCard = ({
  project,
  data,
  projectScope,
  projectDetail,
  className,
  ...props
}: ProjectCardProps) => {
  const [contractorRatingDialogOpen, setContractorRatingDialogOpen] =
    useState(false);

  const handleRateContractorClick = () => {
    setContractorRatingDialogOpen(true);
  };

  const handleContractorRatingComplete = () => {
    setContractorRatingDialogOpen(false);
  };

  const handleContractorRatingGoBack = () => {
    setContractorRatingDialogOpen(false);
  };

  // Check if construction is completed
  const isConstructionCompleted =
    projectDetail?.constructionProjectStatus === "completed";
  return (
    <div
      className={cn(
        `w-full min-w-[412px]  px-6 py-5 bg-white rounded-xl border border-neutrals-G40 relative ${projectScope === "construction" ? "pt-0 px-0" : ""}`,
        className,
      )}
      {...props}
    >
      {projectScope === "construction" ? (
        <>
          <div className="space-y-2 bg-primary-blue-B30 px-6 pt-5 pb-[18px] mb-[12px]">
            <h2 className="text-xl font-semibold text-neutrals-G900">
              {projectDetail?.name}
            </h2>
            <div className="flex gap-1">
              <div className="p-1 rounded border border-neutrals-G50 bg-neutrals-G30 h-[23px] inline-flex items-center">
                <span className="text-xs text-neutrals-G600">
                  #ID {projectDetail?.projectId}
                </span>
              </div>
              <div className="p-1 flex items-center rounded border border-neutrals-G50 bg-neutrals-G30 h-[23px]">
                <MapPin className="size-3.5 mr-1" />
                <span className="text-xs text-neutrals-G600">
                  {projectDetail?.location}
                </span>
              </div>
              <div className="p-1 rounded border border-neutrals-G50 bg-neutrals-G30 h-[23px] inline-flex items-center">
                <span className="text-xs text-neutrals-G600">
                  {projectDetail?.projectType}
                </span>
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="flex mb-5 text-xs justify-between text-neutrals-G600">
          Construction details
          {/* Rate Contractor Button - Only show when construction is completed */}
          {isConstructionCompleted && (
            <Button
              onClick={handleRateContractorClick}
              variant="ghost"
              size="sm"
              className="p-0 py-0 h-4 text-xs justify-end font-medium text-primary-blue-B900 hover:bg-none"
            >
              Rate Contractor
              <ChevronRight size={14} className="mb-[2px]" />
            </Button>
          )}
          {/* <div className=" bottom-0 h-0.5 bg-[#eef5fe] mt-2" /> */}
        </div>
      )}

      <div
        className={` h-[122px] space-y-9 ${projectScope === "construction" ? "px-6" : ""}`}
      >
        <div className="space-y-3.5">
          <div className="grid grid-cols-2 gap-20">
            <div className="flex items-center gap-1">
              <CalenderIcon />
              <span className="text-neutrals-G800 text-sm font-normal">
                {format(new Date(project.startDate), "dd MMM")} -{" "}
                {format(new Date(project.endDate), "dd MMM")}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-name-title inline-flex items-center gap-1">
                <FlagIcon />
                <span className="text-neutrals-G800 font-semibold">
                  {data.milestones.length}
                </span>{" "}
                {data.milestones.length === 1 ? "Milestone" : "Milestones"}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-20">
            <div className="flex items-center gap-1">
              <span className="inline-flex items-center gap-1 text-neutrals-G800 text-sm font-bold">
                <ProductivityIcon />
                {project.workerProductivity?.toFixed(0)}%
                <span className="text-neutrals-G800 text-sm font-normal">
                  Productivity
                </span>
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="inline-flex items-center gap-1 text-neutrals-G800 text-sm font-bold">
                <UsersIcon /> {project.noOfWorker}
              </span>
              <span className="text-neutrals-G800 text-sm font-normal">
                {project.noOfWorker === 1 ? "worker" : "workers"}
              </span>
            </div>
          </div>
        </div>

        <div className="space-y-1">
          <div className="flex justify-between items-center">
            <span className="text-xs text-neutrals-G600">
              Construction Progress
            </span>
            <div className="flex items-center gap-1.5">
              <span className="text-xs text-neutrals-G600">
                {format(new Date(project.projectedEndDate), "dd MMM")}
              </span>
              <DayOfCompletionStatusBadge
                projectedEndDate={project.projectedEndDate}
                expectedEndDate={project.endDate}
                className="shrink-0"
              />
            </div>
          </div>
          <Progress
            value={project.progress}
            variant={
              project.progress < 40
                ? "danger"
                : project.progress < 90
                  ? "warning"
                  : "success"
            }
            className="h-1"
          />
        </div>
      </div>

      {/* Contractor Rating Dialog */}
      <ContractorRatingDialog
        open={contractorRatingDialogOpen}
        onOpenChange={setContractorRatingDialogOpen}
        onGoBack={handleContractorRatingGoBack}
        onComplete={handleContractorRatingComplete}
        contractorName={projectDetail?.contractorOrg?.name}
        contractorLocation={projectDetail?.location}
        contractorId={projectDetail?.contractorOrg?._id || ""}
        projectId={projectDetail?._id || ""}
      />
    </div>
  );
};

export default ConstructionDetailsCard;
