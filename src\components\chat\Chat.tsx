"use client";

import {
  ComponentProps,
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

import { cn, shouldShowDateSeparator, formatChatDate } from "@/lib/utils";
import ChatInput from "./ChatInput";
import Message from "./Message";
import DateSeparator from "./DateSeparator";
import NoDataToShow from "../ui/noDataToShow";
import { CreatedMessage } from "@/types/Socket";
import { User } from "@/types/User";

interface ChatContextType {
  chatId: string;
  messages: CreatedMessage[];
  scrollContainerRef?: React.RefObject<HTMLDivElement>;
}

const ChatContext = createContext<ChatContextType>({
  chatId: "",
  messages: [],
  scrollContainerRef: undefined,
});

const ChatProvider = ({
  value,
  children,
}: { value: ChatContextType } & { children: React.ReactNode }) => {
  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};

const useChatContext = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error("useChatContext must be used within a ChatProvider");
  }
  return context;
};

type ChatProps = ComponentProps<"div"> & {
  chatId: string;
  messages: CreatedMessage[];
};

const Chat = ({ chatId, messages, className, ...props }: ChatProps) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  return (
    <ChatProvider value={{ messages, chatId, scrollContainerRef }}>
      <div
        ref={scrollContainerRef}
        className={cn("relative  flex flex-col overflow-auto", className)}
        {...props}
      />
    </ChatProvider>
  );
};

const FloatingDate = () => {
  const { messages, scrollContainerRef } = useChatContext();
  const [currentDate, setCurrentDate] = useState<string>("");

  useEffect(() => {
    if (!scrollContainerRef?.current || !messages?.length) return;

    const handleScroll = () => {
      const container = scrollContainerRef.current;
      if (!container) return;

      const containerRect = container.getBoundingClientRect();
      const containerTop = containerRect.top;
      const containerHeight = containerRect.height;
      const viewportTop = containerTop + 80; // Account for floating date position

      // Find all date separators
      const dateSeparators = container.querySelectorAll(
        "[data-date-separator]",
      );

      const visibleDateSeparator = null;
      const closestDate = "";

      // Check if the first date separator is still visible (meaning we're at the top)
      if (dateSeparators.length > 0) {
        const firstSeparator = dateSeparators[0];
        const rect = firstSeparator.getBoundingClientRect();
        // If the first date separator is visible in the viewport, hide floating date
        if (rect.bottom > containerTop) {
          setCurrentDate("");
          return;
        }
      }

      // Find the most relevant date for messages currently in view
      let relevantDate = "";

      // Check messages in the viewport to determine current date context
      const messageElements = container.querySelectorAll(
        "[data-message-timestamp]",
      );
      messageElements.forEach((messageEl) => {
        const rect = messageEl.getBoundingClientRect();
        if (
          rect.top >= containerTop &&
          rect.top <= containerTop + containerHeight / 2
        ) {
          const timestamp = messageEl.getAttribute("data-message-timestamp");
          if (timestamp) {
            relevantDate = timestamp;
          }
        }
      });

      // Fallback to first message if no specific message is in view
      if (!relevantDate && messages.length > 0) {
        relevantDate = messages[0].timestamp;
      }

      if (relevantDate) {
        setCurrentDate(formatChatDate(relevantDate));
      }
    };

    const container = scrollContainerRef.current;
    container.addEventListener("scroll", handleScroll);

    // Initial call to set the date
    handleScroll();

    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [messages, scrollContainerRef]);

  if (!currentDate || !messages?.length) return null;

  return (
    <div className="flex justify-center sticky top-6 z-40">
      <div className="h-[35px] flex items-center justify-center rounded-full border border-neutrals-G30 bg-neutrals-G20 px-4 shadow-[0px_8px_20.2px_0px_#002F6C21]">
        <span className="text-sm text-neutrals-G600 font-medium">
          {currentDate}
        </span>
      </div>
    </div>
  );
};

Chat.FloatingDate = FloatingDate;

type MessagesProps = {
  user: User;
  messageTakeFullWidth?: boolean;
};

const Messages = ({ user, messageTakeFullWidth = false }: MessagesProps) => {
  const messageEndRef = useRef<HTMLDivElement>(null);
  const [isDiscussionOpen, setIsDiscussionOpen] = useState(false);

  // const { data: user, isPending: isPendingUser } = useGetUser();

  const { messages } = useChatContext();

  useEffect(() => {
    if (messages) {
      messageEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  const isPrevMessagefromTheSameSender = (
    message: CreatedMessage,
    index: number,
  ) => {
    if (!index) return false;
    if (message.stageName) return false;
    if (messages[index - 1] && messages[index - 1].stageName) return false;
    return messages[index - 1].senderId === message.senderId;
  };
  const isMessageSendByMe = (message: CreatedMessage) => {
    if (message.stageName) return false;
    return message.senderId === user._id;
  };
  return (
    <div className="px-5 flex-1 first:pt-5 ">
      {messages?.length === 0 ? (
        <NoDataToShow className="h-full my-8"> No Chats Found</NoDataToShow>
      ) : (
        <>
          {messages?.map((message, index) => {
            // const isPrevMessagefromTheSameSender =
            //   index !== 0 && messages[index - 1].senderId === message.senderId;

            // const isMessageSendByMe = message.stageName
            //   ? false
            //   : message.senderId === user._id;

            const previousMessage = index > 0 ? messages[index - 1] : null;
            const showDateSeparator = shouldShowDateSeparator(
              message,
              previousMessage,
            );

            return (
              <div key={index} data-message-timestamp={message.timestamp}>
                {showDateSeparator && (
                  <DateSeparator timestamp={message.timestamp} />
                )}
                <Message
                  variant={isMessageSendByMe(message) ? "reciever" : "sender"}
                  className={cn(
                    isPrevMessagefromTheSameSender(message, index)
                      ? "pt-1"
                      : "pt-5",
                  )}
                >
                  <Message.Bubble
                    className={cn(messageTakeFullWidth ? "w-full" : "")}
                  >
                    <Message.Info
                      name={message.sender.name}
                      stageName={message.stageName || ""}
                      className={
                        isPrevMessagefromTheSameSender(message, index)
                          ? "hidden"
                          : ""
                      }
                    />
                    {message.stageGroupId ? (
                      <Message.Card
                        name={message.sender.name}
                        stageId={message.stageGroupId}
                        timestamp={message.timestamp}
                        isDiscussionOpen={isDiscussionOpen}
                        setIsDiscussionOpen={setIsDiscussionOpen}
                      />
                    ) : (
                      <Message.Content {...message} />
                    )}
                  </Message.Bubble>
                </Message>
              </div>
            );
          })}
          <div ref={messageEndRef} />
        </>
      )}
    </div>
  );
};

Chat.Messages = Messages;

const Input = ({
  ...props
}: Omit<ComponentProps<typeof ChatInput>, "chatId">) => {
  const { chatId } = useChatContext();

  return <ChatInput chatId={chatId} {...props} />;
};

Chat.Input = Input;

export default Chat;
