"use client";

import { Download, Eye } from "lucide-react";
import Link from "next/link";
import { format, parseISO } from "date-fns";

import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "../ui/button";
import AssignedTeam from "./AssignedTeam";
import ChatIcon from "../icons/Chat";
// import Settings from "../icons/Settings";
import Calendar from "../icons/Calendar";
import Tick2 from "../icons/Tick2";
import OtherCosts from "./OtherCosts";
import { DesignStage } from "@/types/DesignStage";
import NoDataToShow from "../ui/noDataToShow";
import DocumentActionDropdownMenu from "./DocumentAcionDropdownMenu";
import { cn, downloadFile } from "@/lib/utils";
import useMarkStageAsCompleted from "@/services/designStage/markStageAsCompleted";
import UploadDocumentToStage from "./UploadDocumentToStage";
import Discussion from "./Discussion";
import { useState } from "react";
import EditStage from "./EditStage";
import Completed from "../icons/Completed";

type DesignStageCardProps = {
  stage: DesignStage;
  projectId: string;
  isTeam?: boolean;
  isAdmin?: boolean | null;
};

const DesignStageCard = ({
  stage,
  projectId,
  isTeam,
  isAdmin,
}: DesignStageCardProps) => {
  const [isDiscussionOpen, setIsDiscussionOpen] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  const {
    mutate: markStageAsCompleted,
    isPending: isPendingMarkStageAsCompleted,
  } = useMarkStageAsCompleted(() => {
    setConfirmDialogOpen(false);
  });

  return (
    <>
      <div className="flex flex-col gap-y-4 p-3 rounded-xl border border-neutrals-G40 bg-white text-neutrals-G900">
        <div className="px-2 pb-4 pt-1 flex justify-between gap-x-6 border-b border-[#E6E6E6]">
          <div className="space-y-3">
            <div className="flex gap-x-1 items-center">
              <h5 className="font-semibold text-xl">{stage.stageName}</h5>
              {/* <div className="h-6 flex items-center px-3 text-xs text-[#00142E] font-semibold bg-primary-blue-B50 rounded-full">
                {stage.totalHours} {stage.totalHours === 1 ? "hr" : "hrs"}
              </div> */}
            </div>
            <div className="flex items-start gap-x-8">
              <div className="space-y-2">
                <p className="text-neutrals-G400 text-xs font-medium">Team</p>
                <AssignedTeam team={stage.members} />
              </div>
              {(!isTeam || isAdmin) && (
                <div className="space-y-2">
                  <p className="text-neutrals-G400 text-xs font-medium">
                    Expense
                  </p>
                  <div className="flex items-center gap-x-1">
                    <Button
                      variant="input"
                      className="py-1.5 px-1.5 h-[30px] text-xs text-[#7F8FA4]"
                    >
                      Budget :
                      <span className="ml-1 text-completed font-semibold">
                        {stage.budgetAllocated}%
                      </span>
                    </Button>
                    <OtherCosts
                      totalCosts={stage.totalOtherCosts}
                      data={stage.otherCosts || []}
                      stageId={stage._id}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-col justify-between items-end">
            <div className="flex gap-x-6 text-neutrals-G400 text-sm">
              <button
                onClick={() => setIsDiscussionOpen(true)}
                className="flex gap-x-1 items-center"
              >
                <ChatIcon />
                Discussion
              </button>
              {(!isTeam || isAdmin) && (
                <EditStage
                  projectId={projectId}
                  stage={{
                    ...stage,
                    startDate: new Date(stage.startDate),
                    endDate: new Date(stage.endDate),
                  }}
                />
              )}
            </div>
            <div className="flex gap-x-1 items-center text-sm text-neutrals-G800">
              <Calendar className="size-5" />
              <p className="mt-0.5">
                {stage?.startDate && stage?.endDate ? (
                  `${format(new Date(stage.startDate), "dd MMM")} - ${format(
                    new Date(stage.endDate),
                    "dd MMM",
                  )}`
                ) : (
                  <p className="text-sm text-neutrals-G800">
                    No dates available
                  </p>
                )}
                {/* {format(stage?.startDate, "dd MMM")} -{" "}
                {format(stage?.endDate, "dd MMM")} */}
              </p>
            </div>
          </div>
        </div>
        <div className="space-y-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-10">SI</TableHead>
                <TableHead className="w-1/2">Document Name</TableHead>
                <TableHead>Date of upload</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {stage.documents.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={3}>
                    <NoDataToShow />
                  </TableCell>
                </TableRow>
              ) : (
                stage.documents.map((doc, index) => (
                  <TableRow key={index} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{++index}</TableCell>
                    <TableCell className="break-all">
                      {doc?.filename
                        ? doc?.filename
                        : doc?.s3Link.split("/").pop()}
                    </TableCell>
                    <TableCell>
                      {doc.uploadedAt &&
                        format(parseISO(doc.uploadedAt), "d MMM")}
                    </TableCell>
                    <TableCell className="flex justify-end items-center space-y-0">
                      <button
                        onClick={() =>
                          downloadFile(
                            doc.s3Link,
                            doc.s3Link.split("/").pop() ||
                              `document${doc.uploadedAt}`,
                          )
                        }
                      >
                        <Download className="size-3 mr-4" />
                      </button>
                      <Link href={`${doc.s3Link}`} target="_blank">
                        <Eye className="mr-3 size-3.5" />
                      </Link>
                      <DocumentActionDropdownMenu
                        projectId={projectId}
                        stageId={stage._id}
                        document={doc}
                      />
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          <div className="flex justify-between">
            <UploadDocumentToStage projectId={projectId} stageId={stage._id} />

            {stage.isCompleted ? (
              <Button
                disabled={true}
                className={cn(
                  "px-3 h-9 font-medium gap-x-1.5 bg-[#E2E2E2] border border-[#B9B9B9] text-[#474747]",
                )}
              >
                <Completed />
                Marked as completed{" "}
              </Button>
            ) : (
              <Button
                // onClick={() => markStageAsCompleted({ stageId: stage._id })}
                onClick={() => setConfirmDialogOpen(true)}
                loading={isPendingMarkStageAsCompleted}
                className={cn("px-3 h-9 font-medium gap-x-1.5")}
              >
                <Tick2 />
                Mark as completed{" "}
              </Button>
            )}
          </div>
        </div>
      </div>
      <Discussion
        open={isDiscussionOpen}
        onOpenChange={setIsDiscussionOpen}
        chatGroupId={stage.chatGroupId}
        stageId={stage._id}
      />

      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
          <DialogHeader>
            <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
              Mark this stage as completed?
            </DialogTitle>
          </DialogHeader>
          <p className="text-[#474747] ">
            Doing this will notify the client that this stage is marked as
            completed, This is not reversible.
          </p>
          <DialogFooter className="mt-4">
            <Button
              className="font-[600] text-[#6B6B6B] bg-[#E2E2E2] px-[16px] rounded-[8px]"
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              className="rounded-[8px] px-[16px]"
              onClick={() => {
                markStageAsCompleted({ stageId: stage._id });
                // setConfirmDialogOpen(false);
              }}
              loading={isPendingMarkStageAsCompleted}
              disabled={isPendingMarkStageAsCompleted}
            >
              Yes, mark as complete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DesignStageCard;
