"use client";
import PageHeader from "@/components/layout/pageHeader/PageHeader";
import AddNewItem from "@/components/teamVault/AddNewItem";
import ContentSection from "@/components/teamVault/ContentSection";
import useGetItemsFromTeamVault from "@/services/teamVault/getItems";
import { useState } from "react";

function Page() {
  const [selectedFolder, setSelectedFolder] = useState<string>("root");
  const handleSelectedFolder = (folderId: string) => {
    setSelectedFolder(folderId);
  };

  const {
    data: items,
    isPending: loading,
    isSuccess,
    refetch,
  } = useGetItemsFromTeamVault(selectedFolder, [
    "teamVault",
    selectedFolder,
    // refetchItems.toString(),
  ]);
  return (
    <div className="flex flex-col h-screen">
      <PageHeader>
        <div className="flex flex-col">
          <PageHeader.Heading>Team Vault</PageHeader.Heading>
          <PageHeader.Description>
            Access shared files and documents across your organization. All
            files are securely stored and permission-controlled.
          </PageHeader.Description>
        </div>
        <AddNewItem
          handleRefetchItems={refetch}
          selectedFolder={selectedFolder}
        />
      </PageHeader>
      <div className="flex-1 overflow-y-auto p-4 mt-5">
        <ContentSection
          handleSelectedFolder={handleSelectedFolder}
          loading={loading}
          isSuccess={isSuccess}
          items={items}
          refetch={refetch}
        />
      </div>
    </div>
  );
}

export default Page;
