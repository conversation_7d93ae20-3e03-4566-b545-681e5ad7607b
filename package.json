{"name": "projectsmate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.59.0", "@tanstack/react-query-devtools": "^5.59.0", "@types/filesystem": "^0.0.36", "axios": "^1.7.7", "caniuse-lite": "^1.0.30001727", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "firebase": "^10.13.2", "firebase-admin": "^12.6.0", "html-react-parser": "^5.1.18", "js-cookie": "^3.0.5", "lucide-react": "^0.446.0", "next": "14.2.13", "next-themes": "^0.3.0", "react": "^18", "react-day-picker": "^9.1.3", "react-dom": "^18", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.53.0", "react-intersection-observer": "^9.13.1", "react-toastify": "^10.0.5", "recharts": "^2.15.1", "sanitize-html": "^2.13.1", "socket.io-client": "^4.8.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@hookform/devtools": "^4.3.1", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-day-picker": "^5.3.0", "@types/react-dom": "^18", "@types/sanitize-html": "^2.13.0", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "14.2.13", "eslint-config-prettier": "^9.1.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "postcss": "^8.4.47", "prettier": "3.3.3", "tailwindcss": "^3.4.13", "typescript": "^5"}}