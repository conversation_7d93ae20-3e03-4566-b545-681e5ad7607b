"use client";

import React, { useState, useRef } from "react";
import { Button } from "../ui/button";
import { Plus, FolderPlus } from "lucide-react";
import FileUpload from "../icons/FileUpload";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdownMenu";
import { toast } from "sonner";
import FolderModal from "./FolderModal";
import useAddFolderToTeamVault from "@/services/teamVault/addFolder";
import { useGetS3Url, useUploadToS3 } from "@/services/project/sitedocs-hooks";
import useGetUser from "@/services/auth/getUser";
import Loader from "../ui/loader";
import useAddItemsToTeamVault from "@/services/teamVault/addItem";

function AddNewItem({
  selectedFolder,
  handleRefetchItems,
}: {
  selectedFolder: string;
  handleRefetchItems: () => void;
}) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [folderModalOpen, setFolderModalOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { data: userData, isSuccess: isUserDataSuccess } = useGetUser();
  const { mutate, isPending: loading } = useAddFolderToTeamVault(onSuccess);
  const { mutate: addItemsMutate, isPending: isAddingItems } =
    useAddItemsToTeamVault(onSuccess);
  const { mutateAsync: generateS3Url, isPending: isGeneratingS3Url } =
    useGetS3Url("vault");
  const { mutateAsync: uploadToS3, isPending: isUploadingToS3 } =
    useUploadToS3();

  const handleFileUpload = () => {
    // Trigger the hidden file input to open system file dialog
    fileInputRef.current?.click();
    setDropdownOpen(false);
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0] || null;
    if (!file) {
      return;
    }

    try {
      const signedUrl = await generateS3Url({
        fileName: file.name,
        organisationId: userData?.organisation?._id || "",
      });
      const s3Result = await uploadToS3({
        signedUrl,
        file: file,
      });
      const s3Link = signedUrl.split("?")[0];
      addItemsMutate({
        name: file.name,
        parentId: selectedFolder,
        s3: {
          url: s3Link,
          mimeType: file.type,
          size: s3Result?.config?.data?.size,
        },
      });
    } catch (error: any) {
      console.error("Error uploading document:", error);
      toast.error("Failed to upload document. Please try again.");
    } finally {
      event.target.value = "";
    }
  };

  // Open the folder creation modal
  const handleNewFolder = () => {
    setFolderModalOpen(true);
    setDropdownOpen(false);
  };

  // Handle folder creation submission
  const handleFolderSubmit = async (data: { folderName: string }) => {
    try {
      mutate({
        name: data.folderName,
        parentId: selectedFolder,
      });
    } catch (error) {
      console.error("Error creating folder:", error);
      toast.error("Failed to create folder");
    }
  };

  // Callback for successful item addition
  function onSuccess() {
    handleRefetchItems();
    setFolderModalOpen(false);
    setDropdownOpen(false);
    toast.success("success");
  }

  const isUploadingFile = isGeneratingS3Url || isUploadingToS3 || isAddingItems;
  const isAnyOperationInProgress = loading || isUploadingFile;

  return (
    <div className="relative">
      {isAnyOperationInProgress && (
        <div className="absolute inset-0 bg-white/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-md">
          <Loader className="text-white " size={5} />
        </div>
      )}
      <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            className="gap-x-2 pl-4 pr-3"
            disabled={isAnyOperationInProgress}
          >
            New
            <Plus className="size-[22px] stroke-[2.5px]" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[160px] mt-2">
          <DropdownMenuItem
            onClick={handleFileUpload}
            className="gap-x-2 cursor-pointer"
          >
            <FileUpload className="size-4" />
            <span className="text-sm">File upload</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleNewFolder}
            className="gap-x-2 cursor-pointer"
          >
            <FolderPlus className="size-4 text-sm" />
            <span className="text-sm">New folder</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Hidden file input for system file dialog */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,audio/*,video/*,image/*"
        onChange={handleFileChange}
        className="hidden"
      />

      {/* Folder creation modal */}
      {folderModalOpen && (
        <FolderModal
          open={folderModalOpen}
          onOpenChange={setFolderModalOpen}
          title="Create a new folder"
          buttonText="Create"
          onSubmit={handleFolderSubmit}
          loading={loading}
        />
      )}
    </div>
  );
}

export default AddNewItem;
