"use client";

import { useEffect } from "react";
import { z } from "zod";

import Chat from "@/components/chat/Chat";
import { socket } from "@/lib/socket";
import { CreatedMessage } from "@/types/Socket";
import useGetUser from "@/services/auth/getUser";
import { useChatStore } from "@/store/useChatStore";
import { chatSchema } from "@/schema/chat";
import { useSocket } from "@/hooks/useSocket";
import useGetProjectById from "@/services/project/getProject";
import { useParams } from "next/navigation";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import useGetMessages from "@/services/chat/getMessages";

const DesignDiscussions = () => {
  const { id } = useParams() as { id: string };
  const { data: project, isPending: isPendingProject } = useGetProjectById(id);
  const designDiscussionChatGroupId = project?.designDiscussionChatGroupId;

  const messages = useChatStore((state) => state.designDiscussionMessages);
  const updateMessages = useChatStore(
    (state) => state.updateDesignDiscussionMessages,
  );
  const replaceDesignDiscussionMessagesWithChatId = useChatStore(
    (state) => state.replaceDesignDiscussionMessagesWithChatId,
  );

  const { data: userInfo, isPending: isPendingUserInfo } = useGetUser();

  const {
    data: msgData,
    isPending: isPendingMsgData,
    isFetching: isFetchingMsgData,
  } = useGetMessages({
    groupId: designDiscussionChatGroupId,
  });

  const { connectSocket, disconnectSocket } = useSocket();

  useEffect(() => {
    if (!socket.connected) {
      connectSocket();
    }
    return () => disconnectSocket();
  }, []);

  useEffect(() => {
    if (isFetchingMsgData) return;
    if (!designDiscussionChatGroupId) return;

    if (msgData?.messages) {
      replaceDesignDiscussionMessagesWithChatId(
        designDiscussionChatGroupId,
        msgData?.messages,
      );
    }
  }, [
    designDiscussionChatGroupId,
    isFetchingMsgData,
    msgData?.messages,
    replaceDesignDiscussionMessagesWithChatId,
  ]);

  useEffect(() => {
    const onNewMessage = (newMessage: CreatedMessage) => {
      console.log("newMessage inside design discussions", newMessage);
      const groupId = newMessage.groupId;

      if (!groupId) {
        return console.error("No groupId found");
      }

      const prevMessagesInGroupId = messages?.[groupId];

      if (!prevMessagesInGroupId) {
        updateMessages({
          ...messages,
          [groupId]: [newMessage],
        });
        return;
      }

      updateMessages({
        ...messages,
        [groupId]: [...prevMessagesInGroupId, newMessage],
      });
    };

    socket.on("newMessage", onNewMessage);
    return () => {
      socket.off("newMessage");
    };
  }, [messages, updateMessages]);

  const handleSubmit = (values: z.infer<typeof chatSchema>) => {
    if (!userInfo?.user) return console.error("No user");

    if (!designDiscussionChatGroupId) return console.error("No chat group id");

    socket.emit("createMessage", values);

    const formattedMessage: CreatedMessage = {
      senderId: userInfo.user._id,
      text: values.message,
      imageUrl: values?.image || "",
      timestamp: new Date().toISOString(),
      readBy: "",
      sender: {
        name: userInfo.user.name,
      },
    };

    const prevMessagesInGroupId = messages?.[designDiscussionChatGroupId];

    if (!prevMessagesInGroupId) {
      updateMessages({
        ...messages,
        [designDiscussionChatGroupId]: [formattedMessage],
      });
      return;
    }

    updateMessages({
      ...messages,
      [designDiscussionChatGroupId]: [
        ...prevMessagesInGroupId,
        formattedMessage,
      ],
    });
  };

  console.log("MESSAGES??", messages);

  if (isPendingProject) {
    return <Loader />;
  }

  if (!designDiscussionChatGroupId) {
    console.error("Design discussion group id not found");
    return <ErrorText entity="design discussion group id" />;
  }

  return (
    <div className="flex h-full flex-col gap-y-3">
      <div className="flex items-center py-3.5 border-b border-neutrals-G40">
        <div className="space-y-1 text-neutrals-G900">
          <h4 className="font-semibold">Design Discussions</h4>
          <p className="text-xs text-neutrals-G300">
            Stay updated with the latest discussions
          </p>
        </div>
      </div>
      {designDiscussionChatGroupId && (
        <Chat
          chatId={designDiscussionChatGroupId}
          messages={messages[designDiscussionChatGroupId]}
          className="flex-1 overflow-y-auto scrollbar-hide border border-neutrals-G40 rounded-xl bg-gradient-to-b from-white to-[#F3F8FF]"
        >
          <Chat.FloatingDate />
          {isPendingUserInfo || isPendingMsgData ? (
            <Loader />
          ) : (
            userInfo && <Chat.Messages user={userInfo.user} />
          )}
          <Chat.Input
            onFormSubmit={handleSubmit}
            className="border border-neutrals-G40 rounded-xl"
          />
        </Chat>
      )}
    </div>
  );
};

export default DesignDiscussions;
