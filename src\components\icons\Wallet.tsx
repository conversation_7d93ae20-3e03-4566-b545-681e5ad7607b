import React from "react";

const WalletIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    width="16"
    height="15"
    viewBox="0 0 16 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M3 0.25C2.40326 0.25 1.83097 0.487053 1.40901 0.90901C0.987053 1.33097 0.75 1.90326 0.75 2.5V12.25C0.75 12.8467 0.987053 13.419 1.40901 13.841C1.83097 14.2629 2.40326 14.5 3 14.5H12.75C13.3467 14.5 13.919 14.2629 14.341 13.841C14.7629 13.419 15 12.8467 15 12.25V10.9225C15.4725 10.495 15.75 9.8875 15.75 9.25V5.5C15.75 4.8625 15.4725 4.255 15 3.8275V2.5C15 1.90326 14.7629 1.33097 14.341 0.90901C13.919 0.487053 13.3467 0.25 12.75 0.25H3ZM3 1H12.75C13.1478 1 13.5294 1.15804 13.8107 1.43934C14.092 1.72064 14.25 2.10218 14.25 2.5V3.3775C14.01 3.295 13.755 3.25 13.5 3.25H9C8.40326 3.25 7.83097 3.48705 7.40901 3.90901C6.98705 4.33097 6.75 4.90326 6.75 5.5V9.25C6.75 9.84674 6.98705 10.419 7.40901 10.841C7.83097 11.2629 8.40326 11.5 9 11.5H13.5C13.755 11.5 14.01 11.455 14.25 11.3725V12.25C14.25 12.6478 14.092 13.0294 13.8107 13.3107C13.5294 13.592 13.1478 13.75 12.75 13.75H3C2.60218 13.75 2.22064 13.592 1.93934 13.3107C1.65804 13.0294 1.5 12.6478 1.5 12.25V2.5C1.5 2.10218 1.65804 1.72064 1.93934 1.43934C2.22064 1.15804 2.60218 1 3 1ZM9 4H13.5C13.8978 4 14.2794 4.15804 14.5607 4.43934C14.842 4.72064 15 5.10218 15 5.5V9.25C15 9.64782 14.842 10.0294 14.5607 10.3107C14.2794 10.592 13.8978 10.75 13.5 10.75H9C8.60218 10.75 8.22064 10.592 7.93934 10.3107C7.65804 10.0294 7.5 9.64782 7.5 9.25V5.5C7.5 5.10218 7.65804 4.72064 7.93934 4.43934C8.22064 4.15804 8.60218 4 9 4ZM10.875 5.5C10.3777 5.5 9.90081 5.69754 9.54918 6.04917C9.19754 6.40081 9 6.87772 9 7.375C9 7.87228 9.19754 8.34919 9.54918 8.70082C9.90081 9.05246 10.3777 9.25 10.875 9.25C11.3723 9.25 11.8492 9.05246 12.2008 8.70082C12.5525 8.34919 12.75 7.87228 12.75 7.375C12.75 6.87772 12.5525 6.40081 12.2008 6.04917C11.8492 5.69754 11.3723 5.5 10.875 5.5ZM10.875 6.25C11.1734 6.25 11.4595 6.36853 11.6705 6.5795C11.8815 6.79048 12 7.07663 12 7.375C12 7.67337 11.8815 7.95952 11.6705 8.1705C11.4595 8.38147 11.1734 8.5 10.875 8.5C10.5766 8.5 10.2905 8.38147 10.0795 8.1705C9.86853 7.95952 9.75 7.67337 9.75 7.375C9.75 7.07663 9.86853 6.79048 10.0795 6.5795C10.2905 6.36853 10.5766 6.25 10.875 6.25Z"
      fill="#787878"
      stroke="#787878"
      stroke-width="0.5"
    />
  </svg>
);

export default WalletIcon;
