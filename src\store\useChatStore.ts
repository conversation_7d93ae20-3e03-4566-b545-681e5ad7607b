import { create } from "zustand";

import { designStageSlice, DesignStageSlice } from "./designStageSlice";
import {
  designDiscussionSlice,
  DesignDiscussionSlice,
} from "./designDiscussionSlice";
import {
  ConstructionDiscussionSlice,
  constructionDiscussionSlice,
} from "./constructionDiscussionSlice";
import {
  OrganizationChatSlice,
  organizationChatSlice,
} from "./organizationChatSlice";

export const useChatStore = create<
  DesignStageSlice &
    DesignDiscussionSlice &
    ConstructionDiscussionSlice &
    OrganizationChatSlice
>()((...a) => ({
  ...designStageSlice(...a),
  ...designDiscussionSlice(...a),
  ...constructionDiscussionSlice(...a),
  ...organizationChatSlice(...a),
}));
