"use client";

import { useState, useEffect } from "react";
import Image from "next/image";

import { ProjectData } from "@/types/Project";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import CompletionDialog from "./CompletionDialog";
import ContractorRatingDialog from "./ContractorRatingDialog";
import useUpdateProjectStatus, {
  UpdateProjectStatusPayload,
} from "@/services/project/updateProjectStatus";

type ProjectStatusComponentProps = {
  projectData: ProjectData;
};

type PhaseStatus = "ongoing" | "halted" | "completed";

const ProjectStatusComponent: React.FC<ProjectStatusComponentProps> = ({
  projectData,
}) => {
  // Helper function to convert backend status to frontend status
  const mapBackendToFrontend = (status?: string): PhaseStatus => {
    switch (status) {
      case "ongoing":
        return "ongoing";
      case "halted":
        return "halted";
      case "completed":
        return "completed";
      default:
        return "ongoing";
    }
  };

  // Helper function to convert frontend status to backend status
  const mapFrontendToBackend = (
    status: PhaseStatus,
  ): "ongoing" | "halted" | "completed" => {
    switch (status) {
      case "ongoing":
        return "ongoing";
      case "halted":
        return "halted";
      case "completed":
        return "completed";
    }
  };

  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [designStatus, setDesignStatus] = useState<PhaseStatus>(
    mapBackendToFrontend(projectData.designProjectStatus),
  );
  const [constructionStatus, setConstructionStatus] = useState<PhaseStatus>(
    mapBackendToFrontend(projectData.constructionProjectStatus),
  );
  const [completionDialogOpen, setCompletionDialogOpen] = useState(false);
  const [contractorRatingDialogOpen, setContractorRatingDialogOpen] =
    useState(false);
  const [completionType, setCompletionType] = useState<
    "design" | "construction" | "both" | null
  >(null);
  const [pendingCompletionChanges, setPendingCompletionChanges] =
    useState<UpdateProjectStatusPayload>({});

  // Track original values for change detection
  const [originalDesignStatus, setOriginalDesignStatus] = useState<PhaseStatus>(
    mapBackendToFrontend(projectData.designProjectStatus),
  );
  const [originalConstructionStatus, setOriginalConstructionStatus] =
    useState<PhaseStatus>(
      mapBackendToFrontend(projectData.constructionProjectStatus),
    );

  // Mutation for updating project status
  const updateStatusMutation = useUpdateProjectStatus(projectData._id);

  // Sync status when projectData changes
  useEffect(() => {
    const currentDesignStatus = mapBackendToFrontend(
      projectData.designProjectStatus,
    );
    const currentConstructionStatus = mapBackendToFrontend(
      projectData.constructionProjectStatus,
    );

    setDesignStatus(currentDesignStatus);
    setConstructionStatus(currentConstructionStatus);
    setOriginalDesignStatus(currentDesignStatus);
    setOriginalConstructionStatus(currentConstructionStatus);
  }, [projectData]);

  // Determine which phases are active based on project scope
  const projectScope = projectData.projectScope;
  const hasDesign = projectScope === "design" || projectScope === "both";
  const hasConstruction =
    projectScope === "construction" || projectScope === "both";

  // Reset to current values when dialog opens
  const handleDialogOpen = () => {
    const currentDesignStatus = mapBackendToFrontend(
      projectData.designProjectStatus,
    );
    const currentConstructionStatus = mapBackendToFrontend(
      projectData.constructionProjectStatus,
    );

    setDesignStatus(currentDesignStatus);
    setConstructionStatus(currentConstructionStatus);
    setOriginalDesignStatus(currentDesignStatus);
    setOriginalConstructionStatus(currentConstructionStatus);
    setStatusDialogOpen(true);
  };

  // Handle cancel - reset to original values
  const handleCancel = () => {
    setDesignStatus(originalDesignStatus);
    setConstructionStatus(originalConstructionStatus);
    setStatusDialogOpen(false);
  };

  // Check if any changes have been made
  const hasDesignChanged = hasDesign && designStatus !== originalDesignStatus;
  const hasConstructionChanged =
    hasConstruction && constructionStatus !== originalConstructionStatus;
  const hasAnyChanges = hasDesignChanged || hasConstructionChanged;

  const handleStatusChange = (
    phase: "design" | "construction",
    newStatus: PhaseStatus,
  ) => {
    if (phase === "design") {
      setDesignStatus(newStatus);
    } else {
      setConstructionStatus(newStatus);
    }
  };

  const handleSaveChanges = async () => {
    // Separate immediate changes from completion changes
    const immediateChanges: UpdateProjectStatusPayload = {};
    const completionChanges: UpdateProjectStatusPayload = {};

    // Only include changed design status
    if (hasDesignChanged) {
      if (designStatus === "completed") {
        completionChanges.designProjectStatus = "completed";
      } else {
        immediateChanges.designProjectStatus =
          mapFrontendToBackend(designStatus);
      }
    }

    // Only include changed construction status
    if (hasConstructionChanged) {
      if (constructionStatus === "completed") {
        completionChanges.constructionProjectStatus = "completed";
      } else {
        immediateChanges.constructionProjectStatus =
          mapFrontendToBackend(constructionStatus);
      }
    }

    // Make immediate API calls for ongoing/halted status changes
    let immediateCallSuccess = true;
    if (Object.keys(immediateChanges).length > 0) {
      try {
        await updateStatusMutation.mutateAsync(immediateChanges);
      } catch {
        immediateCallSuccess = false;
        // Error is already handled by the mutation (toast)
        return; // Don't proceed with completion dialog if immediate call fails
      }
    }

    // Handle completion changes only if immediate calls succeeded
    if (Object.keys(completionChanges).length > 0 && immediateCallSuccess) {
      setPendingCompletionChanges(completionChanges);

      // Set completion type for dialog
      const isDesignCompleted =
        completionChanges.designProjectStatus === "completed";
      const isConstructionCompleted =
        completionChanges.constructionProjectStatus === "completed";

      if (isDesignCompleted && isConstructionCompleted) {
        setCompletionType("both");
      } else if (isDesignCompleted) {
        setCompletionType("design");
      } else if (isConstructionCompleted) {
        setCompletionType("construction");
      }

      setCompletionDialogOpen(true);
    } else if (Object.keys(completionChanges).length === 0) {
      // No completion changes, just close dialog
      setStatusDialogOpen(false);
    }
  };

  const handleCompletionConfirm = async () => {
    // Make API call for completion status immediately when "Mark as Completed" is clicked
    if (Object.keys(pendingCompletionChanges).length > 0) {
      try {
        await updateStatusMutation.mutateAsync(pendingCompletionChanges);

        // Reset pending changes
        setPendingCompletionChanges({});

        // Close completion dialog
        setCompletionDialogOpen(false);

        // Check if construction is completed to show contractor rating
        const isConstructionCompleted =
          hasConstruction && constructionStatus === "completed";

        if (isConstructionCompleted) {
          // Show contractor rating dialog
          setContractorRatingDialogOpen(true);
        } else {
          // No contractor rating needed, just close everything
          setStatusDialogOpen(false);
        }
      } catch {
        // Error is handled by mutation (toast)
        // Don't proceed if API call fails
      }
    }
  };

  const handleContractorRatingComplete = () => {
    // API call already made in handleCompletionConfirm, just close dialogs
    setContractorRatingDialogOpen(false);
    setStatusDialogOpen(false);
  };

  const handleContractorRatingGoBack = () => {
    // Since API call was already made, we can't go back to completion dialog
    // Just close contractor rating and main dialog
    setContractorRatingDialogOpen(false);
    setStatusDialogOpen(false);
  };

  const getStatusColor = (status: PhaseStatus) => {
    switch (status) {
      case "ongoing":
        return "text-ongoing";
      case "completed":
        return "text-completed";
      case "halted":
        return "text-halted";
      default:
        return "text-gray-700";
    }
  };

  const getStatusDot = (status: PhaseStatus) => {
    switch (status) {
      case "ongoing":
        return "bg-ongoing";
      case "completed":
        return "bg-completed";
      case "halted":
        return "bg-halted";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <>
      <div
        className="flex items-center gap-4 cursor-pointer border py-3 px-4 rounded-xl bg-[#F7FAFE]"
        onClick={handleDialogOpen}
      >
        {hasDesign && (
          <div className="flex items-center gap-2">
            <Image
              src="/design.png"
              alt="Design"
              width={20}
              height={20}
              className="object-contain"
            />
            <span className="text-sm">Design status:</span>

            <div className="flex items-center gap-1">
              <div
                className={`w-2 h-2 rounded-full ${getStatusDot(designStatus)}`}
              />
              <span
                className={`text-sm font-medium ${getStatusColor(designStatus)}`}
              >
                {designStatus}
              </span>
            </div>
          </div>
        )}
        <div className="bg-primary-blue-B50 w-[1px] h-[22px]"></div>
        {hasConstruction && (
          <div className="flex items-center gap-2">
            <Image
              src="/construction.png"
              alt="Construction"
              width={20}
              height={20}
              className="object-contain"
            />
            <div className="flex items-center gap-1">
              <span className="text-sm">Construction status:</span>

              <div
                className={`w-2 h-2 rounded-full ${getStatusDot(constructionStatus)}`}
              />
              <span
                className={`text-sm font-medium ${getStatusColor(constructionStatus)}`}
              >
                {constructionStatus}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Status Update Dialog */}
      <Dialog open={statusDialogOpen} onOpenChange={setStatusDialogOpen}>
        <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
          <DialogHeader>
            <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
              Update Project Status
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 mt-4">
            {hasDesign && (
              <div className="flex items-center justify-between gap-4">
                <label className="text-sm font-medium text-[#1E1E1E] whitespace-nowrap">
                  Design Phase Status:
                </label>
                <Select
                  value={designStatus}
                  onValueChange={(value: PhaseStatus) =>
                    handleStatusChange("design", value)
                  }
                >
                  <SelectTrigger className="w-[200px]">
                    <div className="flex items-center gap-2">
                      <SelectValue />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ongoing">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-ongoing" />
                        <span className="text-ongoing text-sm font-medium">
                          Ongoing
                        </span>
                      </div>
                    </SelectItem>
                    <SelectItem value="halted">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-halted" />
                        <span className="text-halted text-sm font-medium">
                          Halted
                        </span>{" "}
                      </div>
                    </SelectItem>
                    <SelectItem value="completed">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-completed" />
                        <span className="text-completed text-sm font-medium">
                          Completed
                        </span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {hasConstruction && (
              <div className="flex items-center justify-between gap-4">
                <label className="text-sm font-medium text-[#1E1E1E] whitespace-nowrap">
                  Construction Phase Status:
                </label>
                <Select
                  value={constructionStatus}
                  onValueChange={(value: PhaseStatus) =>
                    handleStatusChange("construction", value)
                  }
                >
                  <SelectTrigger className="w-[200px]">
                    <div className="flex items-center gap-2">
                      <SelectValue />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ongoing">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-ongoing" />
                        <span className="text-ongoing text-sm font-medium">
                          Ongoing
                        </span>
                      </div>
                    </SelectItem>
                    <SelectItem value="halted">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-halted" />
                        <span className="text-halted text-sm font-medium">
                          Halted
                        </span>
                      </div>
                    </SelectItem>
                    <SelectItem value="completed">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-completed" />
                        <span className="text-completed text-sm font-medium">
                          Completed
                        </span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="px-4"
              disabled={updateStatusMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveChanges}
              className="px-4"
              disabled={updateStatusMutation.isPending || !hasAnyChanges}
            >
              {updateStatusMutation.isPending ? "Saving..." : "Save changes"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Completion Dialog */}
      <CompletionDialog
        open={completionDialogOpen}
        onOpenChange={setCompletionDialogOpen}
        completionType={completionType}
        onConfirm={handleCompletionConfirm}
        hasContractorRating={
          hasConstruction && constructionStatus === "completed"
        }
      />

      {/* Contractor Rating Dialog */}
      <ContractorRatingDialog
        open={contractorRatingDialogOpen}
        onOpenChange={setContractorRatingDialogOpen}
        onGoBack={handleContractorRatingGoBack}
        onComplete={handleContractorRatingComplete}
        contractorName={projectData.contractorOrg?.name}
        contractorLocation={projectData.location}
        projectId={projectData._id}
        contractorId={projectData.contractorOrg?._id || ""}
      />
    </>
  );
};

export default ProjectStatusComponent;
