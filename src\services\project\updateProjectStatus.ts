import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import api from "@/lib/api-client";

export type UpdateProjectStatusPayload = {
  designProjectStatus?: "ongoing" | "halted" | "completed";
  constructionProjectStatus?: "ongoing" | "halted" | "completed";
};

const updateProjectStatus = async (
  projectId: string,
  payload: UpdateProjectStatusPayload,
) => {
  const response = await api.patch(
    `/projects/${projectId}/update-design-project-status`,
    payload,
  );
  return response.data;
};

const useUpdateProjectStatus = (projectId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateProjectStatusPayload) =>
      updateProjectStatus(projectId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["project", projectId] });
      toast.success("Project status updated successfully!");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to update project status",
      );
    },
  });
};

export default useUpdateProjectStatus;
