import { useMutation } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { toast } from "sonner";

type RateContractorParams = {
  contractorId: string;
  projectId: string;
  rating: number;
};

const useRateContractor = () => {
  return useMutation({
    mutationFn: async ({
      contractorId,
      projectId,
      rating,
    }: RateContractorParams) => {
      const response = await api.post(`/market-place/${contractorId}/reviews`, {
        projectId,
        rating,
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success("Contractor rating submitted successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to submit contractor rating",
      );
    },
  });
};

export default useRateContractor;
