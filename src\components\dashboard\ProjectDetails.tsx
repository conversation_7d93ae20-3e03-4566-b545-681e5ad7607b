"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useInView } from "react-intersection-observer";
import { z } from "zod";
import { format, differenceInDays } from "date-fns";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { ProjectData } from "@/types/Project";

import useUpdateProject from "@/services/project/updateProject";
import useUpdateProjectStatus, {
  UpdateProjectStatusPayload,
} from "@/services/project/updateProjectStatus";
import {
  Popover,
  <PERSON>over<PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import useContractors from "@/services/contractor/getContractors";
import { LoadingSpinner } from "../ui/loader";
import CalendarIcon from "@/components/icons/Calendar";
import { projectDetailsSchema } from "@/schema/projectDetails";
import { useAuth } from "@/app/contexts/AuthContext";
import useDeleteProject from "@/services/project/deleteProject";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alertDialog";
import DeleteIcon from "@/components/icons/Delete";
import CompletionDialog from "./CompletionDialog";
import ContractorRatingDialog from "./ContractorRatingDialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
type FormData = z.infer<typeof projectDetailsSchema>;

type ProjectDetailsFormProps = {
  projectData: ProjectData;
  onFormStateChange: (isDirty: boolean) => void;
};

const ProjectDetailsForm: React.FC<ProjectDetailsFormProps> = ({
  projectData,
  onFormStateChange,
}) => {
  const router = useRouter();
  const updateProjectMutation = useUpdateProject();
  const updateStatusMutation = useUpdateProjectStatus(projectData._id);

  // Helper functions for status mapping
  const mapBackendToFrontend = (
    status?: string,
  ): "ongoing" | "halted" | "completed" => {
    switch (status) {
      case "ongoing":
        return "ongoing";
      case "halted":
        return "halted";
      case "completed":
        return "completed";
      default:
        return "ongoing";
    }
  };

  const mapFrontendToBackend = (
    status: "ongoing" | "halted" | "completed",
  ): "ongoing" | "halted" | "completed" => {
    switch (status) {
      case "ongoing":
        return "ongoing";
      case "halted":
        return "halted";
      case "completed":
        return "completed";
    }
  };

  const [designCompleteDialogOpen, setDesignCompleteDialogOpen] =
    useState(false);
  const [constructionCompleteDialogOpen, setConstructionCompleteDialogOpen] =
    useState(false);
  const [deleteProjectDialogOpen, setDeleteProjectDialogOpen] = useState(false);

  // Status dialog states
  const [designStatusDialogOpen, setDesignStatusDialogOpen] = useState(false);
  const [constructionStatusDialogOpen, setConstructionStatusDialogOpen] =
    useState(false);

  // Completion flow states
  const [completionDialogOpen, setCompletionDialogOpen] = useState(false);
  const [contractorRatingDialogOpen, setContractorRatingDialogOpen] =
    useState(false);
  const [completionType, setCompletionType] = useState<
    "design" | "construction" | null
  >(null);

  // Status states
  const [designStatus, setDesignStatus] = useState<
    "ongoing" | "halted" | "completed"
  >(mapBackendToFrontend(projectData.designProjectStatus));
  const [constructionStatus, setConstructionStatus] = useState<
    "ongoing" | "halted" | "completed"
  >(mapBackendToFrontend(projectData.constructionProjectStatus));
  const [pendingCompletionChanges, setPendingCompletionChanges] =
    useState<UpdateProjectStatusPayload>({});

  const [originalDesignStatus, setOriginalDesignStatus] = useState<
    "ongoing" | "halted" | "completed"
  >(mapBackendToFrontend(projectData.designProjectStatus));
  const [originalConstructionStatus, setOriginalConstructionStatus] = useState<
    "ongoing" | "halted" | "completed"
  >(mapBackendToFrontend(projectData.constructionProjectStatus));

  const handleDeleteSuccess = () => {
    router.push("/projects");
  };

  const deleteProjectMutation = useDeleteProject(handleDeleteSuccess);

  // Sync status when projectData changes
  useEffect(() => {
    const currentDesignStatus = mapBackendToFrontend(
      projectData.designProjectStatus,
    );
    const currentConstructionStatus = mapBackendToFrontend(
      projectData.constructionProjectStatus,
    );

    setDesignStatus(currentDesignStatus);
    setConstructionStatus(currentConstructionStatus);
    setOriginalDesignStatus(currentDesignStatus);
    setOriginalConstructionStatus(currentConstructionStatus);
  }, [projectData]);

  const stripCountryCode = (whatsappNo: string | null | undefined) => {
    // Handle null, undefined, or empty string cases
    if (!whatsappNo || whatsappNo.trim() === "") {
      return "";
    }

    if (whatsappNo.startsWith("+91")) {
      return whatsappNo.substring(3);
    }
    return whatsappNo;
  };

  const addCountryCode = (whatsappNo: string | null | undefined) => {
    // Handle null, undefined, or empty string cases
    if (!whatsappNo || whatsappNo.trim() === "") {
      return undefined;
    }

    if (!whatsappNo.startsWith("+91")) {
      return `+91${whatsappNo}`;
    }
    return whatsappNo;
  };

  const form = useForm<FormData>({
    resolver: zodResolver(projectDetailsSchema),
    defaultValues: {
      name: projectData.name,
      projectType: (projectData as any).projectType || "Commercial",
      numberOfFloors: (projectData as any).numberOfFloors
        ? String((projectData as any).numberOfFloors)
        : "",
      projectScope: {
        design:
          typeof projectData.projectScope === "string"
            ? projectData.projectScope === "design" ||
              projectData.projectScope === "both"
            : (projectData.projectScope as any)?.design || false,
        construction:
          typeof projectData.projectScope === "string"
            ? projectData.projectScope === "construction" ||
              projectData.projectScope === "both"
            : (projectData.projectScope as any)?.construction || false,
      },
      expectedRevenue: (projectData as any).expectedRevenue
        ? String((projectData as any).expectedRevenue)
        : "",
      requiredMargin: (projectData as any).requiredMargin
        ? String((projectData as any).requiredMargin)
        : "",
      designStartDate: (projectData as any).designStartDate
        ? new Date((projectData as any).designStartDate)
        : undefined,
      designEndDate: (projectData as any).designEndDate
        ? new Date((projectData as any).designEndDate)
        : undefined,
      contractorStartDate: (projectData as any).contractorStartDate
        ? new Date((projectData as any).contractorStartDate)
        : undefined,
      contractorEndDate: (projectData as any).contractorEndDate
        ? new Date((projectData as any).contractorEndDate)
        : undefined,
      contractorOrg: projectData.contractorOrg?._id || "",
      clientName: projectData.clientName,
      clientEmail: projectData.clientEmail,
      clientWhatsAppNo: stripCountryCode(projectData.clientWhatsAppNo),
      location: projectData.location,
      status: projectData.status as "ongoing" | "completed" | "halted",
      startDate: projectData.startDate
        ? new Date(projectData.startDate)
        : undefined,
      endDate: projectData.endDate ? new Date(projectData.endDate) : undefined,
    },
    resetOptions: { keepDirtyValues: true },
  });

  const startDate = form.watch("startDate");
  const endDate = form.watch("endDate");

  const onSubmit = async (data: FormData) => {
    try {
      console.log("Form data received:", data);

      const duration =
        startDate && endDate
          ? differenceInDays(
              new Date(endDate.setHours(23, 59, 59)),
              new Date(startDate.setHours(0, 0, 0)),
            )
          : undefined;

      const payload: any = {
        id: projectData._id,
        // Basic Details
        name: data.name,
        projectType: data.projectType,
        projectScope:
          data.projectScope.design && data.projectScope.construction
            ? "both"
            : data.projectScope.design
              ? "design"
              : "construction",
      };

      // Add optional fields only if they have values
      if (data.numberOfFloors && data.numberOfFloors.trim() !== "") {
        payload.numberOfFloors = parseInt(data.numberOfFloors);
      }
      if (data.expectedRevenue && data.expectedRevenue.trim() !== "") {
        payload.expectedRevenue = parseFloat(data.expectedRevenue);
      }
      if (data.requiredMargin && data.requiredMargin.trim() !== "") {
        payload.requiredMargin = parseFloat(data.requiredMargin);
      }

      // Add timeline fields
      if (data.designStartDate) {
        payload.designStartDate = format(data.designStartDate, "yyyy-MM-dd");
      }
      if (data.designEndDate) {
        payload.designEndDate = format(data.designEndDate, "yyyy-MM-dd");
      }
      if (data.contractorStartDate) {
        payload.contractorStartDate = format(
          data.contractorStartDate,
          "yyyy-MM-dd",
        );
      }
      if (data.contractorEndDate) {
        payload.contractorEndDate = format(
          data.contractorEndDate,
          "yyyy-MM-dd",
        );
      }
      if (data.startDate) {
        payload.startDate = format(data.startDate, "yyyy-MM-dd");
      }
      if (data.endDate) {
        payload.endDate = format(data.endDate, "yyyy-MM-dd");
      }
      if (duration !== undefined) {
        payload.duration = duration;
      }

      // Add client details only if they have values
      if (data.clientName && data.clientName.trim() !== "") {
        payload.clientName = data.clientName;
      }
      if (data.clientEmail && data.clientEmail.trim() !== "") {
        payload.clientEmail = data.clientEmail;
      }
      if (data.clientWhatsAppNo && data.clientWhatsAppNo.trim() !== "") {
        payload.clientWhatsAppNo = addCountryCode(data.clientWhatsAppNo);
      }
      if (data.location && data.location.trim() !== "") {
        payload.location = data.location;
      }

      // Add other fields
      if (data.contractorOrg && data.contractorOrg.trim() !== "") {
        payload.contractorOrg = data.contractorOrg;
      }
      payload.status = data.status;

      console.log("Project update payload:", payload);

      await updateProjectMutation.mutateAsync(payload);
    } catch (error) {
      console.error("Failed to update project:", error);
    }
  };

  useEffect(() => {
    form.reset({
      name: projectData.name,
      projectType: (projectData as any).projectType || "Commercial",
      numberOfFloors: (projectData as any).numberOfFloors
        ? String((projectData as any).numberOfFloors)
        : "",
      projectScope: {
        design:
          typeof projectData.projectScope === "string"
            ? projectData.projectScope === "design" ||
              projectData.projectScope === "both"
            : (projectData.projectScope as any)?.design || false,
        construction:
          typeof projectData.projectScope === "string"
            ? projectData.projectScope === "construction" ||
              projectData.projectScope === "both"
            : (projectData.projectScope as any)?.construction || false,
      },
      expectedRevenue: (projectData as any).expectedRevenue
        ? String((projectData as any).expectedRevenue)
        : "",
      requiredMargin: (projectData as any).requiredMargin
        ? String((projectData as any).requiredMargin)
        : "",
      designStartDate: (projectData as any).designStartDate
        ? new Date((projectData as any).designStartDate)
        : undefined,
      designEndDate: (projectData as any).designEndDate
        ? new Date((projectData as any).designEndDate)
        : undefined,
      contractorStartDate: (projectData as any).contractorStartDate
        ? new Date((projectData as any).contractorStartDate)
        : undefined,
      contractorEndDate: (projectData as any).contractorEndDate
        ? new Date((projectData as any).contractorEndDate)
        : undefined,
      contractorOrg: projectData.contractorOrg?._id || "",
      clientName: projectData.clientName,
      clientEmail: projectData.clientEmail,
      clientWhatsAppNo: stripCountryCode(projectData.clientWhatsAppNo),
      location: projectData.location,
      status: projectData.status as "ongoing" | "completed" | "halted",
      startDate: projectData.startDate
        ? new Date(projectData.startDate)
        : undefined,
      endDate: projectData.endDate ? new Date(projectData.endDate) : undefined,
    });
  }, [projectData, form]);

  useEffect(() => {
    onFormStateChange(form.formState.isDirty);
  }, [form.formState.isDirty, onFormStateChange]);

  const { ref: contractorLoadingRef, inView: contractorInView } = useInView({
    threshold: 0,
    rootMargin: "100px",
  });

  const {
    contractorOptions,
    isLoading,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useContractors({
    limit: 10,
  });

  useEffect(() => {
    if (contractorInView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [contractorInView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  const { isTeamMember: isTeam, isAdmin } = useAuth();

  if (isTeam === null) return null;

  const projectScope = form.watch("projectScope");

  const handleMarkDesignComplete = () => {
    // TODO: Implement mark design as complete API call
    console.log("Mark design as complete");
    setDesignCompleteDialogOpen(false);
  };

  const handleMarkConstructionComplete = () => {
    // TODO: Implement mark construction as complete API call
    console.log("Mark construction as complete");
    setConstructionCompleteDialogOpen(false);
  };

  const handleDeleteProject = () => {
    deleteProjectMutation.mutate(projectData._id);
    setDeleteProjectDialogOpen(false);
  };

  // Dialog open handlers - reset to current values
  const handleDesignStatusDialogOpen = () => {
    const currentStatus = mapBackendToFrontend(projectData.designProjectStatus);
    setDesignStatus(currentStatus);
    setOriginalDesignStatus(currentStatus);
    setDesignStatusDialogOpen(true);
  };

  const handleConstructionStatusDialogOpen = () => {
    const currentStatus = mapBackendToFrontend(
      projectData.constructionProjectStatus,
    );
    setConstructionStatus(currentStatus);
    setOriginalConstructionStatus(currentStatus);
    setConstructionStatusDialogOpen(true);
  };

  // Handle cancel - reset to original values
  const handleDesignCancel = () => {
    setDesignStatus(originalDesignStatus);
    setDesignStatusDialogOpen(false);
  };

  const handleConstructionCancel = () => {
    setConstructionStatus(originalConstructionStatus);
    setConstructionStatusDialogOpen(false);
  };

  // Status change handlers
  const handleDesignStatusSave = async (
    newStatus: "ongoing" | "halted" | "completed",
  ) => {
    setDesignStatus(newStatus);

    if (newStatus === originalDesignStatus) {
      setDesignStatusDialogOpen(false);
      return;
    }

    if (newStatus === "completed") {
      setPendingCompletionChanges({ designProjectStatus: "completed" });
      setCompletionType("design");
      setDesignStatusDialogOpen(false);
      setCompletionDialogOpen(true);
    } else {
      try {
        await updateStatusMutation.mutateAsync({
          designProjectStatus: mapFrontendToBackend(newStatus),
        });
        setDesignStatusDialogOpen(false);
      } catch {}
    }
  };

  const handleConstructionStatusSave = async (
    newStatus: "ongoing" | "halted" | "completed",
  ) => {
    setConstructionStatus(newStatus);

    if (newStatus === originalConstructionStatus) {
      setConstructionStatusDialogOpen(false);
      return;
    }

    if (newStatus === "completed") {
      setPendingCompletionChanges({ constructionProjectStatus: "completed" });
      setCompletionType("construction");
      setConstructionStatusDialogOpen(false);
      setCompletionDialogOpen(true);
    } else {
      try {
        await updateStatusMutation.mutateAsync({
          constructionProjectStatus: mapFrontendToBackend(newStatus),
        });
        setConstructionStatusDialogOpen(false);
      } catch {}
    }
  };

  const handleCompletionConfirm = async () => {
    if (Object.keys(pendingCompletionChanges).length > 0) {
      try {
        await updateStatusMutation.mutateAsync(pendingCompletionChanges);

        setPendingCompletionChanges({});

        setCompletionDialogOpen(false);

        if (completionType === "construction") {
          setContractorRatingDialogOpen(true);
        } else {
        }
      } catch {}
    }
  };

  const handleContractorRatingComplete = () => {
    setContractorRatingDialogOpen(false);
  };

  const handleContractorRatingGoBack = () => {
    setContractorRatingDialogOpen(false);
  };

  return (
    <Form {...form}>
      <form
        id="projectDetailsForm"
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6"
      >
        {/* DIAGNOSTIC: Comment out sections one by one to find the culprit */}

        {/* Basic Details Section */}
        <div className="border border-border-gray1 rounded-xl">
          <h5 className="px-6 py-4 text-neutrals-G800 font-semibold border-b border-[#E6E6E6]">
            Basic details
          </h5>
          <div className="p-6 space-y-6">
            {/* Project name - single row */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project name</FormLabel>
                  <FormControl>
                    <Input
                      disabled={!isAdmin}
                      className="text-base text-name-title"
                      placeholder="Project Name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Type of project and No. of Floors - same row */}
            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="projectType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type of project</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger
                        disabled={!isAdmin}
                        className="data-[placeholder]:text-name-title text-name-title font-medium"
                      >
                        <SelectValue placeholder="Select project type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Commercial">Commercial</SelectItem>
                        <SelectItem value="Residential">Residential</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="numberOfFloors"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>No. of Floors</FormLabel>
                    <FormControl>
                      <Input
                        disabled={!isAdmin}
                        type="number"
                        className="text-base text-name-title"
                        placeholder="Enter number of floors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Project Scope - single row with checkboxes */}
            <div className="flex flex-col space-y-3">
              <FormLabel>Project Scope</FormLabel>
              <div className="grid grid-cols-3">
                <FormField
                  control={form.control}
                  name="projectScope.design"
                  render={({ field }) => (
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel>Design</FormLabel>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="projectScope.construction"
                  render={({ field }) => (
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel>Construction</FormLabel>
                    </FormItem>
                  )}
                />
              </div>
              <FormMessage />
            </div>

            {/* Expected revenue and Margin Required - same row */}
            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="expectedRevenue"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Expected revenue</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#49515b] text-base">
                          Rs
                        </span>
                        <Input
                          disabled={!isAdmin}
                          type="number"
                          className="text-base text-name-title pl-12"
                          placeholder="40000"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="requiredMargin"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Margin Required</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          disabled={!isAdmin}
                          type="number"
                          className="text-base text-name-title pr-8"
                          placeholder="50"
                          {...field}
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#49515b] text-base">
                          %
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>

        {/* Design & Construction sections moved outside form - see after Client Details */}

        {false && (
          <div>
            <div className="px-6 py-4 border-b border-[#E6E6E6]">
              <h5 className="text-neutrals-G800 font-semibold">Design</h5>
            </div>
            <div className="p-6">
              {/* DIAGNOSTIC: Design content commented out for scroll testing */}
              {false && (
                <div className="grid grid-cols-2 gap-6">
                  {/* Design Timeline - Left Half */}
                  <div className="space-y-2">
                    <FormLabel>Design timeline</FormLabel>
                    <div className="flex gap-x-2">
                      <FormField
                        control={form.control}
                        name="designStartDate"
                        render={({ field }) => (
                          <FormItem className="w-1/2">
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    disabled={!isAdmin}
                                    variant="input"
                                    className={cn(
                                      "text-left",
                                      !field.value && "text-neutrals-G100",
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "dd MMM yyyy")
                                    ) : (
                                      <span>Choose date</span>
                                    )}
                                    <CalendarIcon className="ml-auto" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) =>
                                    date < new Date("1900-01-01")
                                  }
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="designEndDate"
                        render={({ field }) => (
                          <FormItem className="w-1/2">
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    disabled={!isAdmin}
                                    variant="input"
                                    className={cn(
                                      "text-left",
                                      !field.value && "text-neutrals-G100",
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "dd MMM yyyy")
                                    ) : (
                                      <span>Choose date</span>
                                    )}
                                    <CalendarIcon className="ml-auto" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) =>
                                    date < new Date("1900-01-01")
                                  }
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Design Status - Right Half */}
                  <div className="space-y-2">
                    <FormLabel>Design Status</FormLabel>
                    <Button
                      type="button"
                      variant="input"
                      disabled={!isAdmin}
                      onClick={handleDesignStatusDialogOpen}
                      className="w-full justify-between text-left data-[placeholder]:text-name-title text-name-title font-medium"
                    >
                      <span className="capitalize">{designStatus}</span>
                      <div className="flex items-center gap-1">
                        <div
                          className={`w-2 h-2 rounded-full ${
                            designStatus === "ongoing"
                              ? "bg-ongoing"
                              : designStatus === "completed"
                                ? "bg-completed"
                                : "bg-halted"
                          }`}
                        />
                      </div>
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* DIAGNOSTIC: Construction Section COMPLETELY REMOVED FOR TESTING */}
        {false && (
          <div className="border border-border-gray1 rounded-xl">
            <div className="px-6 py-4 border-b border-[#E6E6E6]">
              <h5 className="text-neutrals-G800 font-semibold">Construction</h5>
            </div>
            <div className="p-6 space-y-6">
              {/* DIAGNOSTIC: Construction content commented out for scroll testing */}
              {false && (
                <div>
                  <FormField
                    control={form.control}
                    name="contractorOrg"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <>
                            <FormLabel>Select Contractor</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value || ""}
                            >
                              <SelectTrigger
                                disabled={!isAdmin}
                                loading={isLoading}
                                className="data-[placeholder]:text-name-title text-name-title font-medium"
                              >
                                <SelectValue placeholder="Select">
                                  {field.value ===
                                  projectData.contractorOrg?._id
                                    ? projectData.contractorOrg.name
                                    : contractorOptions.find(
                                        (contractor) =>
                                          contractor.value === field.value,
                                      )?.label}
                                </SelectValue>
                              </SelectTrigger>
                              <SelectContent>
                                {contractorOptions.length > 0 ? (
                                  <>
                                    {contractorOptions.map((contractor) => (
                                      <SelectItem
                                        key={contractor.value}
                                        value={contractor.value}
                                      >
                                        {contractor.label}
                                      </SelectItem>
                                    ))}
                                    <SelectItem
                                      ref={contractorLoadingRef}
                                      value={"loading-spinner"}
                                      disabled
                                      className={cn(
                                        "data-[disabled]:opacity-100 justify-center ",
                                        isFetchingNextPage ? "mb-1" : "h-0",
                                      )}
                                    >
                                      {isFetchingNextPage && (
                                        <LoadingSpinner
                                          size={4}
                                          className="border-2 "
                                        />
                                      )}
                                    </SelectItem>
                                  </>
                                ) : (
                                  <SelectItem value="no-contractors" disabled>
                                    No contractors found
                                  </SelectItem>
                                )}
                              </SelectContent>
                            </Select>
                          </>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Second Row - Timeline (Left Half) + Project Status (Right Half) */}
                  <div className="grid grid-cols-2 gap-6">
                    {/* Construction Timeline - Left Half */}
                    <div className="space-y-2">
                      <FormLabel>Construction timeline</FormLabel>
                      <div className="flex gap-x-2">
                        <FormField
                          control={form.control}
                          name="contractorStartDate"
                          render={({ field }) => (
                            <FormItem className="w-1/2">
                              <Popover>
                                <PopoverTrigger asChild>
                                  <FormControl>
                                    <Button
                                      disabled={!isAdmin}
                                      variant="input"
                                      className={cn(
                                        "text-left",
                                        !field.value && "text-neutrals-G100",
                                      )}
                                    >
                                      {field.value ? (
                                        format(field.value, "dd MMM yyyy")
                                      ) : (
                                        <span>Choose date</span>
                                      )}
                                      <CalendarIcon className="ml-auto" />
                                    </Button>
                                  </FormControl>
                                </PopoverTrigger>
                                <PopoverContent
                                  className="w-auto p-0"
                                  align="start"
                                >
                                  <Calendar
                                    mode="single"
                                    selected={field.value}
                                    onSelect={field.onChange}
                                    disabled={(date) =>
                                      date < new Date("1900-01-01")
                                    }
                                  />
                                </PopoverContent>
                              </Popover>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="contractorEndDate"
                          render={({ field }) => (
                            <FormItem className="w-1/2">
                              <Popover>
                                <PopoverTrigger asChild>
                                  <FormControl>
                                    <Button
                                      disabled={!isAdmin}
                                      variant="input"
                                      className={cn(
                                        "text-left",
                                        !field.value && "text-neutrals-G100",
                                      )}
                                    >
                                      {field.value ? (
                                        format(field.value, "dd MMM yyyy")
                                      ) : (
                                        <span>Choose date</span>
                                      )}
                                      <CalendarIcon className="ml-auto" />
                                    </Button>
                                  </FormControl>
                                </PopoverTrigger>
                                <PopoverContent
                                  className="w-auto p-0"
                                  align="start"
                                >
                                  <Calendar
                                    mode="single"
                                    selected={field.value}
                                    onSelect={field.onChange}
                                    disabled={(date) =>
                                      date < new Date("1900-01-01")
                                    }
                                  />
                                </PopoverContent>
                              </Popover>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Construction Status - Right Half */}
                    <div className="space-y-2">
                      <FormLabel>Construction Status</FormLabel>
                      <Button
                        type="button"
                        variant="input"
                        disabled={!isAdmin}
                        onClick={handleConstructionStatusDialogOpen}
                        className="w-full justify-between text-left data-[placeholder]:text-name-title text-name-title font-medium"
                      >
                        <span className="capitalize">{constructionStatus}</span>
                        <div className="flex items-center gap-1">
                          <div
                            className={`w-2 h-2 rounded-full ${
                              constructionStatus === "ongoing"
                                ? "bg-ongoing"
                                : constructionStatus === "completed"
                                  ? "bg-completed"
                                  : "bg-halted"
                            }`}
                          />
                        </div>
                      </Button>
                    </div>
                  </div>
                </div>
              )}
              <p className="text-gray-500 italic">
                Construction section content temporarily removed for scroll
                testing
              </p>
            </div>
          </div>
        )}

        {/* Client Details Section */}
        <div className="border border-border-gray1 rounded-xl">
          <h5 className="px-6 py-4 text-neutrals-G800 font-semibold border-b border-[#E6E6E6]">
            Client details
          </h5>
          <div className="p-6 space-y-6">
            {/* Row 1: Client name + Email */}
            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="clientName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client name</FormLabel>
                    <FormControl>
                      <Input
                        disabled={!isAdmin}
                        className="text-base text-name-title"
                        placeholder="Name"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="clientEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        disabled={!isAdmin}
                        type="email"
                        className="text-base text-name-title"
                        placeholder="Email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Row 2: Client Phone + Location */}
            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="clientWhatsAppNo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client Phone</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <span className="absolute left-3 mt-[1px] top-1/2 transform -translate-y-1/2 text-[#49515b] text-base">
                          +91
                        </span>
                        <Input
                          disabled={!isAdmin}
                          type="tel"
                          className="text-base text-name-title pl-12"
                          placeholder="Enter 10-digit number"
                          maxLength={10}
                          pattern="[0-9]{10}"
                          {...field}
                          onChange={(e) => {
                            // Only allow digits and limit to 10 characters
                            const value = e.target.value
                              .replace(/\D/g, "")
                              .slice(0, 10);
                            field.onChange(value);
                          }}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input
                        disabled={!isAdmin}
                        className="text-base text-name-title"
                        placeholder="Location"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Row 3: Quality Checklist Visibility + Empty */}
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-2 flex justify-between items-center">
                <Label className="text-sm font-medium text-neutrals-G700">
                  Quality Checklist Visibility
                </Label>
                {/* <div className="w-10 h-6 bg-blue-500 rounded-full relative cursor-pointer">
                  <div className="w-4 h-4 bg-white rounded-full absolute top-1 right-1 transition-all duration-200"></div>
                </div> */}
                <Switch />
              </div>
              {/* Second half left empty as per design */}
              <div></div>
            </div>
          </div>
        </div>

        {/* Mark Design as Complete Dialog */}
        <Dialog
          open={designCompleteDialogOpen}
          onOpenChange={setDesignCompleteDialogOpen}
        >
          <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
            <DialogHeader>
              <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
                Mark Design as Complete?
              </DialogTitle>
            </DialogHeader>
            <p className="text-[#474747]">
              Once marked as complete, this action cannot be undone. Are you
              sure you want to proceed?
            </p>
            <DialogFooter className="mt-4">
              <Button
                className="font-[600] text-[#6B6B6B] bg-[#E2E2E2] px-[16px] rounded-[8px]"
                variant="outline"
                onClick={() => setDesignCompleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                className="rounded-[8px] px-[16px]"
                onClick={handleMarkDesignComplete}
              >
                Yes, mark as complete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Mark Construction as Complete Dialog */}
        <Dialog
          open={constructionCompleteDialogOpen}
          onOpenChange={setConstructionCompleteDialogOpen}
        >
          <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
            <DialogHeader>
              <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
                Mark Construction as Complete?
              </DialogTitle>
            </DialogHeader>
            <p className="text-[#474747]">
              Once marked as complete, this action cannot be undone. Are you
              sure you want to proceed?
            </p>
            <DialogFooter className="mt-4">
              <Button
                className="font-[600] text-[#6B6B6B] bg-[#E2E2E2] px-[16px] rounded-[8px]"
                variant="outline"
                onClick={() => setConstructionCompleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                className="rounded-[8px] px-[16px]"
                onClick={handleMarkConstructionComplete}
              >
                Yes, mark as complete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Project Dialog */}
        <AlertDialog
          open={deleteProjectDialogOpen}
          onOpenChange={setDeleteProjectDialogOpen}
        >
          <AlertDialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
                Delete Project
              </AlertDialogTitle>
              <AlertDialogDescription className="text-[#474747]">
                Are you sure you want to permanently delete this project? This
                action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="mt-4">
              <AlertDialogCancel className="font-[600] text-[#6B6B6B] bg-[#E2E2E2] px-[16px] rounded-[8px]">
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                className="rounded-[8px] px-[16px] bg-red-600 hover:bg-red-700"
                onClick={handleDeleteProject}
                disabled={deleteProjectMutation.isPending}
              >
                {deleteProjectMutation.isPending
                  ? "Deleting..."
                  : "Yes, Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Design Status Dialog */}
        <Dialog
          open={designStatusDialogOpen}
          onOpenChange={setDesignStatusDialogOpen}
        >
          <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
            <DialogHeader>
              <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
                Update Design Status
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              <div className="flex items-center justify-between gap-4">
                <label className="text-sm font-medium text-[#1E1E1E] whitespace-nowrap">
                  Design Phase Status:
                </label>
                <Select
                  value={designStatus}
                  onValueChange={(value: "ongoing" | "halted" | "completed") =>
                    setDesignStatus(value)
                  }
                >
                  <SelectTrigger className="w-[200px]">
                    <div className="flex items-center gap-2">
                      <SelectValue />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ongoing">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-ongoing" />
                        <span className="text-ongoing text-sm font-medium">
                          Ongoing
                        </span>
                      </div>
                    </SelectItem>
                    <SelectItem value="halted">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-halted" />
                        <span className="text-halted text-sm font-medium">
                          Halted
                        </span>{" "}
                      </div>
                    </SelectItem>
                    <SelectItem value="completed">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-completed" />
                        <span className="text-completed text-sm font-medium">
                          Completed
                        </span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex justify-end gap-3 mt-6">
              <Button
                variant="outline"
                onClick={handleDesignCancel}
                className="px-4"
                disabled={updateStatusMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleDesignStatusSave(designStatus)}
                className="px-4"
                disabled={
                  updateStatusMutation.isPending ||
                  designStatus === originalDesignStatus
                }
              >
                {updateStatusMutation.isPending ? "Saving..." : "Save changes"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Construction Status Dialog */}
        <Dialog
          open={constructionStatusDialogOpen}
          onOpenChange={setConstructionStatusDialogOpen}
        >
          <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
            <DialogHeader>
              <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
                Update Construction Status
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              <div className="flex items-center justify-between gap-4">
                <label className="text-sm font-medium text-[#1E1E1E] whitespace-nowrap">
                  Construction Phase Status:
                </label>
                <Select
                  value={constructionStatus}
                  onValueChange={(value: "ongoing" | "halted" | "completed") =>
                    setConstructionStatus(value)
                  }
                >
                  <SelectTrigger className="w-[200px]">
                    <div className="flex items-center gap-2">
                      <SelectValue />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ongoing">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-ongoing" />
                        <span className="text-ongoing text-sm font-medium">
                          Ongoing
                        </span>
                      </div>
                    </SelectItem>
                    <SelectItem value="halted">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-halted" />
                        <span className="text-halted text-sm font-medium">
                          Halted
                        </span>
                      </div>
                    </SelectItem>
                    <SelectItem value="completed">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-completed" />
                        <span className="text-completed text-sm font-medium">
                          Completed
                        </span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex justify-end gap-3 mt-6">
              <Button
                variant="outline"
                onClick={handleConstructionCancel}
                className="px-4"
                disabled={updateStatusMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleConstructionStatusSave(constructionStatus)}
                className="px-4"
                disabled={
                  updateStatusMutation.isPending ||
                  constructionStatus === originalConstructionStatus
                }
              >
                {updateStatusMutation.isPending ? "Saving..." : "Save changes"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Completion Dialog */}
        <CompletionDialog
          open={completionDialogOpen}
          onOpenChange={setCompletionDialogOpen}
          completionType={completionType}
          onConfirm={handleCompletionConfirm}
          hasContractorRating={completionType === "construction"}
        />

        {/* Contractor Rating Dialog */}
        <ContractorRatingDialog
          open={contractorRatingDialogOpen}
          onOpenChange={setContractorRatingDialogOpen}
          onGoBack={handleContractorRatingGoBack}
          onComplete={handleContractorRatingComplete}
          contractorName={projectData.contractorOrg?.name}
          contractorLocation={projectData.location}
          contractorId={projectData.contractorOrg?._id || ""}
          projectId={projectData._id}
        />
      </form>

      {/* Design & Construction Sections - Outside form to fix scroll issue */}
      <div className="space-y-6 mt-6">
        {/* Design Section - Show if design checkbox is checked */}
        {projectScope?.design && (
          <div className="border border-border-gray1 rounded-xl">
            <div className="px-6 py-4 border-b border-[#E6E6E6]">
              <h5 className="text-neutrals-G800 font-semibold">Design</h5>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 gap-6">
                {/* Design Timeline - Left Half */}
                <div className="space-y-2">
                  <FormLabel>Design timeline</FormLabel>
                  <div className="flex gap-x-2">
                    <FormField
                      control={form.control}
                      name="designStartDate"
                      render={({ field }) => (
                        <FormItem className="w-1/2">
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  disabled={!isAdmin}
                                  variant="input"
                                  className={cn(
                                    "text-left",
                                    !field.value && "text-neutrals-G100",
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "dd MMM yyyy")
                                  ) : (
                                    <span>Choose date</span>
                                  )}
                                  <CalendarIcon className="ml-auto" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date < new Date("1900-01-01")
                                }
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="designEndDate"
                      render={({ field }) => (
                        <FormItem className="w-1/2">
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  disabled={!isAdmin}
                                  variant="input"
                                  className={cn(
                                    "text-left",
                                    !field.value && "text-neutrals-G100",
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "dd MMM yyyy")
                                  ) : (
                                    <span>Choose date</span>
                                  )}
                                  <CalendarIcon className="ml-auto" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date < new Date("1900-01-01")
                                }
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Design Status - Right Half */}
                <div className="space-y-2">
                  <FormLabel>Design Status</FormLabel>
                  <Button
                    type="button"
                    variant="input"
                    disabled={!isAdmin}
                    onClick={handleDesignStatusDialogOpen}
                    className="w-full justify-between text-left data-[placeholder]:text-name-title text-name-title font-medium"
                  >
                    <span className="capitalize">{designStatus}</span>
                    <div className="flex items-center gap-1">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          designStatus === "ongoing"
                            ? "bg-ongoing"
                            : designStatus === "completed"
                              ? "bg-completed"
                              : "bg-halted"
                        }`}
                      />
                    </div>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Construction Section - Show if construction checkbox is checked */}
        {projectScope?.construction && (
          <div className="border border-border-gray1 rounded-xl">
            <div className="px-6 py-4 border-b border-[#E6E6E6]">
              <h5 className="text-neutrals-G800 font-semibold">Construction</h5>
            </div>
            <div className="p-6 space-y-6">
              {/* Select Contractor - Full Width */}
              <FormField
                control={form.control}
                name="contractorOrg"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <>
                        <FormLabel>Select Contractor</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || ""}
                        >
                          <SelectTrigger
                            disabled={!isAdmin}
                            loading={isLoading}
                            className="data-[placeholder]:text-name-title text-name-title font-medium"
                          >
                            <SelectValue placeholder="Select">
                              {field.value === projectData.contractorOrg?._id
                                ? projectData.contractorOrg.name
                                : contractorOptions.find(
                                    (contractor) =>
                                      contractor.value === field.value,
                                  )?.label}
                            </SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            {contractorOptions.length > 0 ? (
                              <>
                                {contractorOptions.map((contractor) => (
                                  <SelectItem
                                    key={contractor.value}
                                    value={contractor.value}
                                  >
                                    {contractor.label}
                                  </SelectItem>
                                ))}
                                <SelectItem
                                  ref={contractorLoadingRef}
                                  value={"loading-spinner"}
                                  disabled
                                  className={cn(
                                    "data-[disabled]:opacity-100 justify-center ",
                                    isFetchingNextPage ? "mb-1" : "h-0",
                                  )}
                                >
                                  {isFetchingNextPage && (
                                    <LoadingSpinner className="h-4 w-4" />
                                  )}
                                </SelectItem>
                              </>
                            ) : (
                              <SelectItem value="no-contractors" disabled>
                                No contractors available
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      </>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Construction Timeline and Status */}
              <div className="grid grid-cols-2 gap-6">
                {/* Construction Timeline - Left Half */}
                <div className="space-y-2">
                  <FormLabel>Construction timeline</FormLabel>
                  <div className="flex gap-x-2">
                    <FormField
                      control={form.control}
                      name="contractorStartDate"
                      render={({ field }) => (
                        <FormItem className="w-1/2">
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  disabled={!isAdmin}
                                  variant="input"
                                  className={cn(
                                    "text-left",
                                    !field.value && "text-neutrals-G100",
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "dd MMM yyyy")
                                  ) : (
                                    <span>Choose date</span>
                                  )}
                                  <CalendarIcon className="ml-auto" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date < new Date("1900-01-01")
                                }
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="contractorEndDate"
                      render={({ field }) => (
                        <FormItem className="w-1/2">
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  disabled={!isAdmin}
                                  variant="input"
                                  className={cn(
                                    "text-left",
                                    !field.value && "text-neutrals-G100",
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "dd MMM yyyy")
                                  ) : (
                                    <span>Choose date</span>
                                  )}
                                  <CalendarIcon className="ml-auto" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date < new Date("1900-01-01")
                                }
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Construction Status - Right Half */}
                <div className="space-y-2">
                  <FormLabel>Construction Status</FormLabel>
                  <Button
                    type="button"
                    variant="input"
                    disabled={!isAdmin}
                    onClick={handleConstructionStatusDialogOpen}
                    className="w-full justify-between text-left data-[placeholder]:text-name-title text-name-title font-medium"
                  >
                    <span className="capitalize">{constructionStatus}</span>
                    <div className="flex items-center gap-1">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          constructionStatus === "ongoing"
                            ? "bg-ongoing"
                            : constructionStatus === "completed"
                              ? "bg-completed"
                              : "bg-halted"
                        }`}
                      />
                    </div>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
        {/* Critical Actions Section */}
        <div className="pb-3">
          <div className=" w-1/2 border border-border-gray1 rounded-xl">
            <h5 className="px-6 py-4 text-neutrals-G800 font-semibold border-b border-[#E6E6E6]">
              Critical Actions
            </h5>
            <div className="p-6">
              {/* Half width delete section */}
              <div
                className="flex items-center justify-between gap-3 cursor-pointer"
                onClick={() => setDeleteProjectDialogOpen(true)}
              >
                <span className="text-red-500 font-medium">
                  Delete this project permanently
                </span>
                <DeleteIcon className="text-red-500" />
              </div>
            </div>
          </div>
          {/* Second half left empty as per design */}
          <div></div>
        </div>
      </div>
    </Form>
  );
};

export default ProjectDetailsForm;
