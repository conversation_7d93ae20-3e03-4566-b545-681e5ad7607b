import { cva, VariantProps } from "class-variance-authority";
import { ChevronRight, UserRound } from "lucide-react";
import { ComponentProps } from "react";
import Image from "next/image";
import { format } from "date-fns";
import ChatIcon from "../icons/Chat";
import { cn } from "@/lib/utils";
import { CreatedMessage } from "@/types/Socket";
import Discussion from "../designStage/Discussion";

const messageVariants = cva("group/message fle w-full", {
  variants: {
    variant: {
      // sender: "grid-cols-[auto_1fr] place-items-start",
      // reciever: "grid-cols-[1fr_auto] place-items-end",
      sender: "flex flex-row",
      reciever: "flex flex-row-reverse",
    },
  },
  defaultVariants: {
    variant: "reciever",
  },
});

type MessageProps = ComponentProps<"div"> &
  VariantProps<typeof messageVariants>;

const Message = ({ variant, className, ...props }: MessageProps) => {
  return (
    <div
      data-variant={variant}
      className={cn(messageVariants({ variant, className }))}
      {...props}
    />
  );
};

const Bubble = ({ className, ...props }: ComponentProps<"div">) => {
  return <div className={cn("w-[51%] text-sm", className)} {...props} />;
};

Message.Bubble = Bubble;

type InfoProps = ComponentProps<"div"> & {
  name: string;
  stageName: string;
};

const Info = ({ name, stageName, className, ...props }: InfoProps) => {
  console.log(stageName, "stageName in message info");
  return (
    <div
      className={cn(
        "group-[&[data-variant=sender]]/message:bg-primary-blue-B30 flex group-[&[data-variant=sender]]/message:justify-self-start justify-self-end  items-center py-2 pr-4 relative top-[1.2px] pl-3 bg-primary-blue-B900 w-fit line-break-all gap-x-1 rounded-t-[8px] border-x border-t border-white group-[&[data-variant=sender]]/message:border-primary-blue-B50",
        className,
      )}
      {...props}
    >
      {stageName ? (
        <ChatIcon className="size-4 group-[&[data-variant=sender]]/message:inline-flex hidden" />
      ) : (
        <UserRound className="size-3 text-primary-blue-B900 group-[&[data-variant=sender]]/message:inline-flex hidden" />
      )}
      <p className="group-[&[data-variant=sender]]/message:text-neutrals-G900 text-white">
        {stageName || name}
      </p>
    </div>
  );
};

Message.Info = Info;

type ContentProps = CreatedMessage & { className?: string };

const Content = ({ imageUrl, text, timestamp, className }: ContentProps) => {
  return (
    <div
      className={cn(
        "p-3.5 flex flex-col items-end group-[&[data-variant=sender]]/message:bg-primary-blue-B30 bg-primary-blue-B900 border border-white group-[&[data-variant=sender]]/message:border-primary-blue-B50 rounded-b-[8px] group-[&[data-variant=sender]]/message:rounded-tr-[8px] rounded-tl-[8px]",
        className,
      )}
    >
      <div
        className={cn(
          "relative w-full aspect-[470.5/313.67]",
          !imageUrl ? "hidden" : "",
        )}
      >
        {imageUrl && (
          <Image src={imageUrl} fill alt="image" className="rounded-[8px]" />
        )}
      </div>
      <p className="group-[&[data-variant=sender]]/message:text-neutrals-G600 text-white text-sm">
        {text}
      </p>
      <div className="group-[&[data-variant=sender]]/message:text-neutrals-G200 text-neutrals-G50 text-xs">
        {format(timestamp, "h:mm a")}
      </div>
    </div>
  );
};

Message.Content = Content;

type CardProps = {
  className?: string;
  name: string;
  stageId: string;
  timestamp: string;
  isDiscussionOpen: boolean;
  setIsDiscussionOpen: (open: boolean) => void;
};
const Card = ({
  className,
  name,
  stageId,
  timestamp,
  isDiscussionOpen,
  setIsDiscussionOpen,
}: CardProps) => {
  const handleClick = () => {
    setIsDiscussionOpen(true);
  };
  return (
    <>
      <div
        className={cn(
          "p-3.5 flex flex-col gap-y-4 group-[&[data-variant=sender]]/message:bg-primary-blue-B30 bg-primary-blue-B900 border border-white group-[&[data-variant=sender]]/message:border-primary-blue-B50 rounded-b-[8px] group-[&[data-variant=sender]]/message:rounded-tr-[8px] rounded-tl-[8px]",
          className,
        )}
      >
        <div className="flex justify-between">
          <h4 className="text-sm font-semibold"></h4>
          <div
            onClick={handleClick}
            className="cursor-pointer flex gap-x-1 p-2 rounded-lg border-2 border-primary-blue-B60 "
          >
            <h5 className="text-xs font-semibold text-primary-blue-B900">
              Click to view
            </h5>
            <ChevronRight className="size-4 my-auto font-semibold text-primary-blue-B900" />
          </div>
        </div>
        {/* bottom section */}
        <div className="flex justify-between">
          <h5 className="text-xs">
            Last messaged by <span className="font-bold">{name}</span>
          </h5>
          <div className="group-[&[data-variant=sender]]/message:text-neutrals-G200 text-neutrals-G50 text-xs">
            {format(timestamp, "h:mm a")}
          </div>
        </div>
      </div>
      <Discussion
        open={isDiscussionOpen}
        onOpenChange={setIsDiscussionOpen}
        chatGroupId={stageId}
      />
    </>
  );
};
Message.Card = Card;
export default Message;
