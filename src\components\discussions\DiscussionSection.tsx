"use client";
import React, { useEffect } from "react";
import { z } from "zod";
import { socket } from "@/lib/socket";

import Chat from "../chat/Chat";
import Loader from "../ui/loader";
import { chatSchema } from "@/schema/chat";
import { useChatStore } from "@/store/useChatStore";
import { CreatedMessage } from "@/types/Socket";
import useGetUser from "@/services/auth/getUser";
import useGetMessages from "@/services/chat/getMessages";
import { useSocket } from "@/hooks/useSocket";

function DiscussionSection() {
  const { data: userInfo, isPending: isPendingUserInfo } = useGetUser();
  const { connectSocket, disconnectSocket } = useSocket();

  const organizationChatGroupId = userInfo?.organisation?.discussionId;

  const messages = useChatStore((state) => state.organizationChatMessages);
  const updateMessages = useChatStore(
    (state) => state.updateOrganizationChatMessages,
  );
  const replaceOrganizationChatMessagesWithChatId = useChatStore(
    (state) => state.replaceOrganizationChatMessagesWithChatId,
  );

  const { data: msgData, isPending: isPendingMsgData } = useGetMessages({
    groupId: organizationChatGroupId,
  });

  const getMessagesByChatId = useChatStore(
    (state) => state.getOrganizationChatMessagesByChatId,
  );

  const organizationMessages = organizationChatGroupId
    ? getMessagesByChatId(organizationChatGroupId)
    : [];

  useEffect(() => {
    if (!socket.connected) {
      connectSocket();
    }
    return () => disconnectSocket();
  }, []);
  useEffect(() => {
    if (msgData?.messages && organizationChatGroupId) {
      replaceOrganizationChatMessagesWithChatId(
        organizationChatGroupId,
        msgData.messages,
      );
    }
  }, [
    msgData,
    organizationChatGroupId,
    replaceOrganizationChatMessagesWithChatId,
  ]);

  useEffect(() => {
    if (!organizationChatGroupId) return;

    const handleNewMessage = (message: CreatedMessage) => {
      if (message.groupId === organizationChatGroupId) {
        const prevMessagesInGroupId = messages?.[organizationChatGroupId];

        if (!prevMessagesInGroupId) {
          updateMessages({
            ...messages,
            [organizationChatGroupId]: [message],
          });
          return;
        }

        updateMessages({
          ...messages,
          [organizationChatGroupId]: [...prevMessagesInGroupId, message],
        });
      }
    };

    socket.on("newMessage", handleNewMessage);

    return () => {
      socket.off("newMessage", handleNewMessage);
    };
  }, [organizationChatGroupId, messages, updateMessages]);

  const handleSubmit = (values: z.infer<typeof chatSchema>) => {
    if (!userInfo?.user) return console.error("No user");

    if (!organizationChatGroupId)
      return console.error("No organization chat group id");

    socket.emit("createMessage", {
      ...values,
      groupId: organizationChatGroupId,
    });

    const formattedMessage: CreatedMessage = {
      senderId: userInfo.user._id,
      text: values.message,
      imageUrl: values?.image || "",
      timestamp: new Date().toISOString(),
      readBy: "",
      sender: {
        name: userInfo.user.name,
      },
    };

    const prevMessagesInGroupId = messages?.[organizationChatGroupId];

    if (!prevMessagesInGroupId) {
      updateMessages({
        ...messages,
        [organizationChatGroupId]: [formattedMessage],
      });
      return;
    }

    updateMessages({
      ...messages,
      [organizationChatGroupId]: [...prevMessagesInGroupId, formattedMessage],
    });
  };

  if (!organizationChatGroupId) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center text-neutrals-G300">
          <p>Organization chat is not available.</p>
          <p className="text-sm">Please contact your administrator.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <Chat
        chatId={organizationChatGroupId}
        messages={organizationMessages}
        className="flex-1 overflow-y-auto scrollbar-hide border border-neutrals-G40 rounded-xl bg-gradient-to-b from-white to-[#F3F8FF]"
      >
        <Chat.FloatingDate />
        {isPendingUserInfo || isPendingMsgData ? (
          <Loader />
        ) : (
          userInfo && <Chat.Messages user={userInfo.user} />
        )}

        <Chat.Input
          onFormSubmit={handleSubmit}
          className="border border-neutrals-G40 rounded-xl"
        />
      </Chat>
    </div>
  );
}

export default DiscussionSection;
