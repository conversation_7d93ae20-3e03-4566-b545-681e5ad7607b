"use client";
import { ComponentProps, useEffect } from "react";
import { z } from "zod";
import { socket } from "@/lib/socket";

import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import Chat from "../chat/Chat";
import { chatSchema } from "@/schema/chat";
import { useChatStore } from "@/store/useChatStore";
import { CreatedMessage } from "@/types/Socket";
import useGetUser from "@/services/auth/getUser";
import useGetMessages from "@/services/chat/getMessages";
import Loader from "../ui/loader";
import { useSocket } from "@/hooks/useSocket";

type DocumentHistoryProps = Pick<
  ComponentProps<typeof Sheet>,
  "open" | "onOpenChange"
> & {
  chatGroupId: string;
  stageId?: string;
  useHigherZIndex?: boolean;
};

const Discussion = ({
  open,
  onOpenChange,
  chatGroupId,
  stageId,
  useHigherZIndex = false,
}: DocumentHistoryProps) => {
  const { data: userData, isPending: isPendingUserData } = useGetUser();

  const allMessages = useChatStore((state) => state.messages);
  const updateMessages = useChatStore((state) => state.updateMessages);
  const { connectSocket, disconnectSocket } = useSocket();

  const {
    data: msgData,
    isPending: isPendingMsgData,
    isFetching: isFetchingMsgData,
  } = useGetMessages({
    groupId: chatGroupId,
  });

  const getMessagesByChatId = useChatStore(
    (state) => state.getMessagesByChatId,
  );

  const messages = getMessagesByChatId(chatGroupId);

  const replaceMessagesWithChatId = useChatStore(
    (state) => state.replaceMessagesWithChatId,
  );

  useEffect(() => {
    if (isFetchingMsgData) return;

    if (msgData?.messages) {
      replaceMessagesWithChatId(chatGroupId, msgData?.messages);
    }
  }, [
    chatGroupId,
    isFetchingMsgData,
    msgData?.messages,
    replaceMessagesWithChatId,
  ]);
  useEffect(() => {
    if (!socket.connected) {
      connectSocket();
    }
    return () => {
      console.log("cleanup disconnecting socket");
      disconnectSocket();
    };
  }, []);

  const handleSubmit = (values: z.infer<typeof chatSchema>) => {
    if (!userData?.user) return;
    socket.emit("createMessage", { ...values, fromStageGroup: true, stageId });

    const formattedMessage: CreatedMessage = {
      senderId: userData.user._id,
      text: values.message,
      imageUrl: values?.image || "",
      timestamp: new Date().toISOString(),
      readBy: "",
      sender: {
        name: userData.user.name,
      },
    };

    const prevMessagesInGroupId = allMessages?.[chatGroupId];

    // if messages doesn't exists for this id
    if (!prevMessagesInGroupId) {
      updateMessages({
        ...allMessages,
        [chatGroupId]: [formattedMessage],
      });
      return;
    }

    //else
    updateMessages({
      ...allMessages,
      [chatGroupId]: [...prevMessagesInGroupId, formattedMessage],
    });
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        className={`max-w-[507px] flex flex-col gap-0 px-0 pb-0 ${
          useHigherZIndex ? "z-[60]" : ""
        }`}
        overlayClassName={useHigherZIndex ? "z-[55]" : undefined}
      >
        <SheetHeader className=" px-5">
          <SheetTitle>Stage Discussions</SheetTitle>
        </SheetHeader>
        {isPendingUserData || isPendingMsgData ? (
          <Loader />
        ) : (
          userData && (
            <Chat
              chatId={chatGroupId}
              messages={messages}
              className="flex-1 overflow-y-auto scrollbar-hide"
            >
              <Chat.FloatingDate />
              <Chat.Messages user={userData.user} messageTakeFullWidth />
              <Chat.Input
                onFormSubmit={handleSubmit}
                className="border-t border-neutrals-G40"
              />
            </Chat>
          )
        )}
      </SheetContent>
    </Sheet>
  );
};

export default Discussion;
